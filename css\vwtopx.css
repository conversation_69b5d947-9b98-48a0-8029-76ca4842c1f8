@font-face {
  font-family: '思源宋体';
  src: url(https://ztimg.hefei.cc/static/common/fonts/思源宋体.otf);
}
@font-face {
  font-family: 'Uranus_Pixel_11Px';
  src: url(https://ztimg.hefei.cc/static/common/fonts/Uranus_Pixel_11Px.ttf);
}
body,
div,
h1,
h2,
h3,
h4,
h5,
h6,
html,
li,
p,
span {
  font-size: 2.1vh;
}
.musicbtn {
  width: 2.4vh;
  height: 2.4vh;
  top: 1.2vh;
  right: 0.3vh;
  z-index: 11;
}
.warp {
  margin: 0 auto;
  min-height: 101.95vh;
  height: 100vh;
  width: 59.97vh;
  font-family: '思源宋体';
  background: #008042;
}
.warp .page {
  width: 100%;
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  z-index: 2;
  background: url(../img/bj.jpg?v=15) no-repeat center center / 100% auto;
  color: #352219;
}
.warp .page .logo {
  width: 12.39vh;
  position: absolute;
  top: 1.2vh;
  left: 1.2vh;
}
.warp .page .start {
  width: 34.06vh;
  position: absolute;
  bottom: 12vh;
  transform: translateY(50%);
}
.warp .page .tip {
  color: #fff;
  font-size: 3vh;
  position: absolute;
  bottom: 2vh;
  transform: translateY(50%) scale(0.5);
  text-align: center;
}
.warp .page .button_container {
  display: flex;
  justify-content: center;
  position: absolute;
  bottom: 7vh;
  transform: translateY(50%);
}
.warp .page .button_container .button {
  width: 18.23vh;
  margin: 0 1.2vh;
}
.warp .page .home {
  width: 100%;
  flex-shrink: 0;
}
.warp .page .time {
  width: 13.43vh;
  height: 7.84vh;
  background: url(../img/time.png?v=15) no-repeat center center / 100% 100%;
  position: fixed;
  top: 6vh;
  display: flex;
  justify-content: center;
  align-items: center;
  font-family: 'Uranus_Pixel_11Px';
  color: #f0f0f0;
  font-weight: bold;
  font-size: 3.6vh;
  letter-spacing: 0.18vh;
  padding-left: 0.78vh;
  padding-bottom: 0.6vh;
}
.warp .page .time2 {
  width: 13.43vh;
  height: 7.84vh;
  background: url(../img/time.png?v=15) no-repeat center center / 100% 100%;
  position: fixed;
  top: 6vh;
  display: flex;
  justify-content: center;
  align-items: center;
  font-family: 'Uranus_Pixel_11Px';
  color: #f0f0f0;
  font-weight: bold;
  font-size: 3.6vh;
  letter-spacing: 0.18vh;
  padding-left: 0.78vh;
  padding-bottom: 0.6vh;
  transform: scale(1.5);
}
.warp .page .menu {
  width: 58.37vh;
  height: 9.68vh;
  background: url(../img/menu.png?v=15) no-repeat center center / 100% 100%;
  position: fixed;
  bottom: 4.2vh;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 5.4vh;
}
.warp .page .menu .plate {
  width: 5.36vh;
  height: 4.56vh;
  background: url(../img/plate.png?v=15) no-repeat center center / 100% 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  flex-direction: column;
  margin-top: -2.4vh;
}
.warp .page .menu .plate .text {
  font-size: 3vh;
  position: absolute;
  bottom: -3vh;
  color: #007f43;
  color: #000;
  transform: scale(0.5);
  white-space: nowrap;
}
.warp .page .menu .plate .food {
  width: 4.2vh;
  height: 3vh;
  object-fit: contain;
  transition: all 0.3s ease;
}
.warp .page .menu .plate.found {
  animation: plateFound 0.8s ease-out;
}
.warp .page .menu .plate.found .food {
  animation: foodFound 1.2s ease-out;
}
.warp .page .menu .plate.found::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 7.2vh;
  height: 7.2vh;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(255, 215, 0, 0.3) 0%, rgba(255, 255, 255, 0.2) 50%, transparent 70%);
  animation: haloEffect 1.5s ease-out;
  pointer-events: none;
  z-index: -1;
}
.warp .page .menu .plate.found::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 2px;
  height: 2px;
  background: #fff;
  border-radius: 50%;
  box-shadow: 0 0 6px #fff, 0 0 12px #fff, 0 0 18px #ffd700, 0 0 24px #ffd700;
  animation: sparkleEffect 1s ease-out;
  pointer-events: none;
}
.warp .page .scroll-hint {
  position: fixed;
  bottom: 14.39vh;
  z-index: 20;
  pointer-events: none;
  display: flex;
  align-items: center;
}
.warp .page .scroll-hint .scroll-hint-text {
  font-size: 3vh;
  color: #fff;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
  margin-bottom: 1.2vh;
  font-weight: bold;
}
.warp .page .scroll-hint .scroll-hint-arrow {
  margin-left: 0.6vh;
  margin-top: 0vh;
  font-size: 3vh;
  color: #fff;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
  animation: scrollArrowBounce 1s ease-in-out infinite;
}
.warp .page .zu {
  width: 35.74vh;
  position: absolute;
  bottom: 0;
  left: 0;
}
.warp .page .tu {
  width: 40.46vh;
  position: absolute;
  top: 17.27vh;
  left: 0;
}
.warp .page .yu {
  width: 41.58vh;
  position: absolute;
  top: 52.77vh;
  right: 0;
}
.warp .page .people {
  width: 34.3vh;
  position: absolute;
  top: 74.96vh;
  left: 0;
}
.warp .page .light {
  width: 3.6vh;
  height: 3.6vh;
  position: absolute;
  pointer-events: none;
  animation: rotate 40s linear infinite;
  /* 中心亮点 */
  /* 四个闪光点 */
}
.warp .page .light::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 0.9vh;
  height: 0.9vh;
  border-radius: 50%;
  background: #ffffff;
  box-shadow: 0 0 0.6vh rgba(255, 255, 255, 0.8), 0 0 1.2vh rgba(255, 215, 0, 0.6);
  animation: centerGlow 1.5s ease-in-out infinite;
}
.warp .page .light .sparkle {
  position: absolute;
  width: 0.48vh;
  height: 0.48vh;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
}
.warp .page .light .sparkle:nth-child(1) {
  top: -0.6vh;
  left: 50%;
  transform: translateX(-50%);
  animation: sparkle1 2.5s ease-in-out infinite;
}
.warp .page .light .sparkle:nth-child(2) {
  top: 50%;
  right: -0.6vh;
  transform: translateY(-50%);
  animation: sparkle2 2.5s ease-in-out infinite 0.6s;
}
.warp .page .light .sparkle:nth-child(3) {
  bottom: -0.6vh;
  left: 50%;
  transform: translateX(-50%);
  animation: sparkle3 2.5s ease-in-out infinite 1.2s;
}
.warp .page .light .sparkle:nth-child(4) {
  top: 50%;
  left: -0.6vh;
  transform: translateY(-50%);
  animation: sparkle4 2.5s ease-in-out infinite 1.8s;
}
.warp .page .light1 {
  top: 44.38vh;
  left: 14.99vh;
}
.warp .page .light2 {
  top: 50.97vh;
  left: 5.4vh;
}
.warp .page .light3 {
  top: 92.95vh;
  left: 10.79vh;
}
.warp .page .light4 {
  top: 112.14vh;
  left: 40.78vh;
}
.warp .page .light5 {
  top: 111.54vh;
  left: 9vh;
}
.warp .page .light6 {
  top: 151.12vh;
  left: 14.99vh;
}
.warp .page .toucharea {
  width: 100%;
  height: 100%;
  position: absolute;
}
.warp .page .toucharea .touchblock {
  width: 6vh;
  height: 6vh;
  background-color: rgba(255, 255, 255, 0);
  position: absolute;
  top: 6vh;
  left: 6vh;
}
.warp .page .toucharea .touchblock1 {
  width: 6vh;
  height: 6vh;
  top: 41.98vh;
  left: 11.39vh;
}
.warp .page .toucharea .touchblock2 {
  width: 8.4vh;
  height: 8.4vh;
  top: 44.98vh;
  left: 19.19vh;
}
.warp .page .toucharea .touchblock3 {
  width: 12.59vh;
  height: 10.79vh;
  top: 63.57vh;
  left: 47.38vh;
}
.warp .page .toucharea .touchblock4 {
  width: 13.79vh;
  height: 10.79vh;
  top: 71.96vh;
  left: 34.78vh;
}
.warp .page .toucharea .touchblock5 {
  width: 9vh;
  height: 9vh;
  top: 86.96vh;
  left: 7.2vh;
}
.warp .page .toucharea .touchblock6 {
  width: 15.59vh;
  height: 29.39vh;
  top: 142.13vh;
  left: 44.38vh;
}
.warp .page .toucharea .touchblock7 {
  width: 25.19vh;
  height: 30.58vh;
  top: 171.51vh;
  left: 34.78vh;
}
.warp .page .toucharea .touchblock8 {
  width: 29.99vh;
  height: 46.18vh;
  top: 187.11vh;
  left: 0vh;
}
.warp .page .toucharea .tip-image {
  height: 6vh;
  width: auto;
  position: absolute;
  z-index: 15;
  pointer-events: none;
  animation: tipShow 1.5s ease-out forwards;
  transform-origin: center center;
}
.warp .page0 {
  background: none;
  height: auto;
}
.warp .page2 {
  background: #82858f no-repeat center center / 100% 100%;
  min-height: 101.95vh;
}
.warp .page2 .bj2 {
  width: 59.97vh;
  position: absolute;
  margin-top: 2.4vh;
}
.warp .page2 .canvas {
  margin-top: 16.19vh;
  width: 59.97vh;
  height: 106.67vh;
  background: transparent;
  cursor: pointer;
  position: absolute;
}
.warp .page2 .button10 {
  width: 23.99vh;
  position: absolute;
  left: 50%;
  bottom: 6vh;
  transform: translate(-50%, 50%);
}
.warp .loadingbg {
  background: url(../img/loading.jpg?v=15) no-repeat center center / 100% 100%;
}
.warp .loadingbg .loading {
  margin-top: 18.59vh;
  width: 29.99vh;
}
.blur {
  filter: blur(0.6vh);
}
.fc {
  justify-content: center;
}
.mask {
  z-index: 10;
  position: fixed;
  top: 0;
  left: 0;
  min-height: 100vh;
  width: 59.97vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  background: rgba(18, 45, 29, 0.3);
  transform: translateX(-50%);
  left: 50%;
  color: #352219;
  font-weight: 300;
}
.mask .popup {
  margin-top: -0.6vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
}
.mask .popup .stit {
  width: 24.71vh;
  height: 6vh;
  background: url(../img/stit.png?v=15) no-repeat center top / 100% 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 3.28vh;
  color: #fff;
  font-weight: bold;
  flex-shrink: 0;
}
.mask .popup .back {
  width: 28.39vh;
  position: absolute;
  bottom: 3.6vh;
}
.mask .popup .back2 {
  width: 28.39vh;
  position: absolute;
  bottom: 5.4vh;
}
.mask .popup .button12 {
  width: 28.39vh;
  height: 8.64vh;
  background: url(../img/button12.png?v=15) no-repeat center top / 100% 100%;
  position: absolute;
  bottom: 5.4vh;
  overflow: hidden;
}
.mask .popup .button12_1 {
  width: 28.39vh;
  height: 8.64vh;
  background: url(../img/button12.png?v=15) no-repeat center top / 100% 100%;
  position: absolute;
  bottom: 3.6vh;
  overflow: hidden;
}
.mask .popup .close {
  width: 7.36vh;
  height: 7.04vh;
  background: url(../img/close.png?v=15) no-repeat center top / 100% 100%;
  position: absolute;
  top: 1.2vh;
  right: 0vh;
}
.mask .popup1 {
  width: 53.01vh;
  height: 88.04vh;
  background: url(../img/area.png?v=15) no-repeat center top / 100% 100%;
  justify-content: flex-start;
  padding: 10.79vh 0vh 2.4vh;
}
.mask .popup1 .rule {
  margin: 1.2vh 3.6vh 11.99vh;
  height: 100%;
  overflow-y: auto;
  text-align: justify;
  line-height: 1.8;
  font-size: 2.48vh;
}
.mask .popup1 .rule div {
  font-size: 2.48vh;
}
.mask .popup1 .rule span {
  font-size: 2.48vh;
}
.mask .popup1 .rule p {
  font-size: 2.48vh;
}
.mask .popup2 {
  width: 53.01vh;
  height: 55.73vh;
  background: url(../img/area2.png?v=15) no-repeat center top / 100% 100%;
  justify-content: flex-start;
  padding: 10.79vh 0vh 2.4vh;
}
.mask .popup2 .prize {
  margin-top: 3.6vh;
  width: 43.66vh;
  height: 16.31vh;
}
.mask .popup2 .no_prize {
  margin-top: 3.6vh;
  width: 43.66vh;
  height: 16.31vh;
  color: #007f43;
  font-size: 3.36vh;
  font-weight: bold;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  font-feature-settings: "halt";
  line-height: 1.6;
}
.mask .popup2 .button5 {
  width: 28.39vh;
  height: 8.64vh;
  background: url(../img/button5.png?v=15) no-repeat center top / 100% 100%;
  position: absolute;
  bottom: 7.2vh;
  overflow: hidden;
}
.mask .popup2 .button6 {
  width: 28.39vh;
  height: 8.64vh;
  background: url(../img/button6.png?v=15) no-repeat center top / 100% 100%;
  position: absolute;
  bottom: 7.2vh;
  overflow: hidden;
}
.mask .popup3 {
  width: 53.01vh;
  height: 69.17vh;
  background: url(../img/area3.png?v=15) no-repeat center top / 100% 100%;
  justify-content: flex-start;
}
.mask .popup4 {
  width: 53.01vh;
  height: 62.77vh;
  background: url(../img/area4.png?v=15) no-repeat center top / 100% 100%;
  justify-content: flex-start;
}
.mask .popup4 .back {
  width: 28.39vh;
  position: absolute;
  bottom: 5.4vh;
  margin-right: 2.4vh;
}
.mask .popup5 {
  width: 53.01vh;
  height: 59.65vh;
  background: url(../img/area5.png?v=15) no-repeat center top / 100% 100%;
  justify-content: flex-start;
}
.mask .popup6 {
  width: 53.01vh;
  height: 59.65vh;
  background: url(../img/area6.png?v=15) no-repeat center top / 100% 100%;
  justify-content: flex-start;
}
.mask .popup7 {
  width: 53.01vh;
  height: 62.77vh;
  background: url(../img/area7.png?v=15) no-repeat center top / 100% 100%;
  justify-content: flex-start;
}
.mask .popup8 {
  width: 53.01vh;
  height: 62.77vh;
  background: url(../img/area8.png?v=15) no-repeat center top / 100% 100%;
  justify-content: flex-start;
  padding-top: 37.78vh;
}
.mask .popup8 .p1 {
  color: #007f43;
  font-size: 2.88vh;
  font-weight: bold;
  font-feature-settings: "halt";
  max-width: 44.98vh;
  text-align: center;
}
.mask .popup8 .p2 {
  margin-top: 1.2vh;
  color: #007f43;
  font-size: 2.48vh;
  font-weight: bold;
  font-feature-settings: "halt";
}
.mask .popup9 {
  width: 53.01vh;
  height: 59.65vh;
  background: url(../img/area9.png?v=15) no-repeat center top / 100% 100%;
  justify-content: flex-start;
}
.mask .popup10 {
  width: 53.01vh;
  height: 59.65vh;
  background: url(../img/area10.png?v=15) no-repeat center top / 100% 100%;
  justify-content: flex-start;
}
.mask .popup10 .button14 {
  animation: button-pulse 1.5s ease-in-out infinite;
}
.mask .popup10 .hand {
  width: 6.6vh;
  position: absolute;
  bottom: 4.2vh;
  right: 10.79vh;
  pointer-events: none;
  animation: hand-point 0.8s ease-in-out infinite;
}
@keyframes elem3 {
  from {
    transform: scale(4);
    opacity: 0;
  }
  to {
    transform: scale(1.2);
    opacity: 1;
  }
}
.elem3 {
  animation: elem3 0.6s ease both;
}
@keyframes tipShow {
  0% {
    transform: scale(0) rotate(0deg);
    opacity: 0;
  }
  20% {
    transform: scale(1.2) rotate(5deg);
    opacity: 1;
  }
  40% {
    transform: scale(1) rotate(-3deg);
    opacity: 1;
  }
  60% {
    transform: scale(1.1) rotate(2deg);
    opacity: 1;
  }
  80% {
    transform: scale(1) rotate(0deg);
    opacity: 1;
  }
  100% {
    transform: scale(0.8);
    opacity: 0;
  }
}
@keyframes centerGlow {
  0% {
    opacity: 0.8;
    transform: translate(-50%, -50%) scale(1);
    box-shadow: 0 0 0.6vh rgba(255, 255, 255, 0.6), 0 0 1.2vh rgba(255, 215, 0, 0.4);
  }
  50% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1.2);
    box-shadow: 0 0 0.9vh #ffffff, 0 0 1.8vh rgba(255, 215, 0, 0.8);
  }
  100% {
    opacity: 0.8;
    transform: translate(-50%, -50%) scale(1);
    box-shadow: 0 0 0.6vh rgba(255, 255, 255, 0.6), 0 0 1.2vh rgba(255, 215, 0, 0.4);
  }
}
@keyframes sparkle1 {
  0%,
  80% {
    opacity: 0;
    transform: translateX(-50%) scale(0);
  }
  10%,
  70% {
    opacity: 1;
    transform: translateX(-50%) scale(1.5);
  }
  40% {
    opacity: 0.5;
    transform: translateX(-50%) scale(1);
  }
}
@keyframes sparkle2 {
  0%,
  80% {
    opacity: 0;
    transform: translateY(-50%) scale(0);
  }
  10%,
  70% {
    opacity: 1;
    transform: translateY(-50%) scale(1.5);
  }
  40% {
    opacity: 0.5;
    transform: translateY(-50%) scale(1);
  }
}
@keyframes sparkle3 {
  0%,
  80% {
    opacity: 0;
    transform: translateX(-50%) scale(0);
  }
  10%,
  70% {
    opacity: 1;
    transform: translateX(-50%) scale(1.5);
  }
  40% {
    opacity: 0.5;
    transform: translateX(-50%) scale(1);
  }
}
@keyframes sparkle4 {
  0%,
  80% {
    opacity: 0;
    transform: translateY(-50%) scale(0);
  }
  10%,
  70% {
    opacity: 1;
    transform: translateY(-50%) scale(1.5);
  }
  40% {
    opacity: 0.5;
    transform: translateY(-50%) scale(1);
  }
}
@keyframes rotate {
  0% {
    transform: translate(-50%, -50%) rotate(0deg);
  }
  100% {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}
@keyframes plateFound {
  0% {
    transform: scale(1);
  }
  20% {
    transform: scale(1.15);
  }
  40% {
    transform: scale(0.95);
  }
  60% {
    transform: scale(1.05);
  }
  80% {
    transform: scale(0.98);
  }
  100% {
    transform: scale(1);
  }
}
@keyframes foodFound {
  0% {
    transform: scale(1) rotate(0deg);
    filter: brightness(1);
  }
  15% {
    transform: scale(1.3) rotate(5deg);
    filter: brightness(1.3) saturate(1.5);
  }
  30% {
    transform: scale(1.1) rotate(-3deg);
    filter: brightness(1.2) saturate(1.3);
  }
  45% {
    transform: scale(1.2) rotate(2deg);
    filter: brightness(1.25) saturate(1.4);
  }
  60% {
    transform: scale(1.05) rotate(-1deg);
    filter: brightness(1.1) saturate(1.2);
  }
  80% {
    transform: scale(1.08) rotate(0deg);
    filter: brightness(1.15) saturate(1.25);
  }
  100% {
    transform: scale(1) rotate(0deg);
    filter: brightness(1.1) saturate(1.2);
  }
}
@keyframes haloEffect {
  0% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.5);
  }
  30% {
    opacity: 0.8;
    transform: translate(-50%, -50%) scale(1.2);
  }
  70% {
    opacity: 0.4;
    transform: translate(-50%, -50%) scale(1.5);
  }
  100% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(2);
  }
}
@keyframes sparkleEffect {
  0% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0);
  }
  20% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
  40% {
    opacity: 0.8;
    transform: translate(-50%, -50%) scale(1.5);
  }
  60% {
    opacity: 0.6;
    transform: translate(-50%, -50%) scale(2);
  }
  80% {
    opacity: 0.3;
    transform: translate(-50%, -50%) scale(2.5);
  }
  100% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(3);
  }
}
.food-canvas {
  pointer-events: none;
  /* 让点击事件穿透到下层canvas */
}
/* 食材动画效果 */
@keyframes foodToPot {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.8;
  }
  100% {
    transform: scale(0.8);
    opacity: 1;
  }
}
/* 掉落食材样式 */
.falling-food {
  position: fixed;
  width: 50px;
  height: 50px;
  object-fit: contain;
  z-index: 1000;
  pointer-events: none;
  transition: all 0.1s ease-out;
}
@keyframes scrollArrowBounce {
  0%,
  100% {
    transform: translateY(-1.2vh);
  }
  50% {
    transform: translateY(0);
  }
}
strong {
  font-weight: bold !important;
}
/* Button14 放大缩小动效 */
@keyframes button-pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}
/* Hand 双击动效 */
@keyframes hand-point {
  0% {
    transform: scale(1);
  }
  10% {
    transform: scale(0.9);
  }
  20% {
    transform: scale(1);
  }
  30% {
    transform: scale(0.9);
  }
  40% {
    transform: scale(1);
  }
  100% {
    transform: scale(1);
  }
}
