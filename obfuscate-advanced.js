const fs = require('fs');
const path = require('path');
const JavaScriptObfuscator = require('javascript-obfuscator');

/**
 * 高级JavaScript代码混淆工具
 * 提供多种预设配置和自定义选项
 */
class AdvancedJSObfuscator {
    constructor() {
        this.inputFile = 'main.js';
        this.outputFile = 'main.obfuscated.js';
        this.backupFile = 'main.backup.js';
        
        // 预设配置
        this.presets = {
            // 轻度混淆 - 保持较好的性能和兼容性
            light: {
                compact: true,
                controlFlowFlattening: false,
                deadCodeInjection: false,
                debugProtection: false,
                disableConsoleOutput: false,
                identifierNamesGenerator: 'hexadecimal',
                renameGlobals: false,
                stringArray: true,
                stringArrayEncoding: [],
                stringArrayThreshold: 0.5,
                transformObjectKeys: false,
                unicodeEscapeSequence: false
            },
            
            // 中度混淆 - 平衡安全性和性能
            medium: {
                compact: true,
                controlFlowFlattening: true,
                controlFlowFlatteningThreshold: 0.5,
                deadCodeInjection: true,
                deadCodeInjectionThreshold: 0.2,
                debugProtection: false,
                disableConsoleOutput: false,
                identifierNamesGenerator: 'hexadecimal',
                renameGlobals: false,
                stringArray: true,
                stringArrayEncoding: ['base64'],
                stringArrayThreshold: 0.75,
                transformObjectKeys: true,
                unicodeEscapeSequence: false,
                splitStrings: true,
                splitStringsChunkLength: 5
            },
            
            // 高度混淆 - 最大安全性，可能影响性能
            high: {
                compact: true, // 压缩代码
                controlFlowFlattening: true, // 控制流平坦化
                controlFlowFlatteningThreshold: 0.75, // 控制流平坦化阈值
                deadCodeInjection: true, // 注入死代码
                deadCodeInjectionThreshold: 0.4, // 注入死代码阈值
                debugProtection: false, // 谨慎启用
                disableConsoleOutput: true, // 禁用console输出
                identifierNamesGenerator: 'mangled', // 标识符名称生成器
                renameGlobals: false, // 谨慎启用
                stringArray: true, // 字符串数组
                stringArrayCallsTransform: true, // 字符串数组调用转换
                stringArrayEncoding: ['base64', 'rc4'], // 字符串编码方式
                stringArrayIndexShift: true, // 字符串数组索引偏移
                stringArrayRotate: true, // 字符串数组旋转
                stringArrayShuffle: true, // 字符串数组洗牌
                stringArrayWrappersCount: 3, // 字符串数组包装器数量
                stringArrayWrappersChainedCalls: true, // 字符串数组包装器链式调用
                stringArrayThreshold: 0.8, // 字符串数组阈值
                transformObjectKeys: true, // 转换对象键
                unicodeEscapeSequence: false, // Unicode转义序列
                splitStrings: true, // 分割字符串
                splitStringsChunkLength: 3, // 分割字符串块长度
                selfDefending: false // 谨慎启用
            },
            
            // 生产环境配置 - 适合生产部署
            production: {
                compact: true,
                controlFlowFlattening: true,
                controlFlowFlatteningThreshold: 0.6,
                deadCodeInjection: true,
                deadCodeInjectionThreshold: 0.3,
                debugProtection: false,
                disableConsoleOutput: true,
                identifierNamesGenerator: 'hexadecimal',
                renameGlobals: false,
                stringArray: true,
                stringArrayEncoding: ['base64'],
                stringArrayThreshold: 0.7,
                transformObjectKeys: true,
                splitStrings: true,
                splitStringsChunkLength: 8,
                target: 'browser'
            }
        };
    }

    /**
     * 显示帮助信息
     */
    showHelp() {
        console.log(`
🔧 高级JavaScript代码混淆工具

用法: node obfuscate-advanced.js [选项]

选项:
  --preset <name>     使用预设配置 (light|medium|high|production)
  --input <file>      指定输入文件 (默认: main.js)
  --output <file>     指定输出文件 (默认: main.obfuscated.js)
  --no-backup        不创建备份文件
  --help             显示此帮助信息

预设配置说明:
  light      - 轻度混淆，保持性能和兼容性
  medium     - 中度混淆，平衡安全性和性能 (默认)
  high       - 高度混淆，最大安全性
  production - 生产环境配置

示例:
  node obfuscate-advanced.js --preset high
  node obfuscate-advanced.js --input app.js --output app.min.js
  node obfuscate-advanced.js --preset production --no-backup
        `);
    }

    /**
     * 解析命令行参数
     */
    parseArguments() {
        const args = process.argv.slice(2);
        const options = {
            preset: 'medium',
            createBackup: true
        };

        for (let i = 0; i < args.length; i++) {
            switch (args[i]) {
                case '--help':
                case '-h':
                    this.showHelp();
                    process.exit(0);
                    break;
                case '--preset':
                    if (i + 1 < args.length) {
                        options.preset = args[++i];
                    }
                    break;
                case '--input':
                    if (i + 1 < args.length) {
                        this.inputFile = args[++i];
                    }
                    break;
                case '--output':
                    if (i + 1 < args.length) {
                        this.outputFile = args[++i];
                    }
                    break;
                case '--no-backup':
                    options.createBackup = false;
                    break;
                default:
                    console.warn(`⚠ 未知参数: ${args[i]}`);
            }
        }

        return options;
    }

    /**
     * 获取混淆配置
     */
    getObfuscationConfig(preset) {
        if (!this.presets[preset]) {
            console.warn(`⚠ 未知预设: ${preset}，使用默认配置 'medium'`);
            preset = 'medium';
        }

        console.log(`📋 使用预设配置: ${preset}`);
        return this.presets[preset];
    }

    /**
     * 检查输入文件
     */
    checkInputFile() {
        if (!fs.existsSync(this.inputFile)) {
            throw new Error(`输入文件 ${this.inputFile} 不存在！`);
        }
        
        const stats = fs.statSync(this.inputFile);
        console.log(`✓ 找到输入文件: ${this.inputFile} (${(stats.size / 1024).toFixed(2)} KB)`);
    }

    /**
     * 创建备份文件
     */
    createBackup() {
        try {
            // 生成带时间戳的备份文件名
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, -5);
            this.backupFile = `${path.parse(this.inputFile).name}.backup.${timestamp}.js`;
            
            fs.copyFileSync(this.inputFile, this.backupFile);
            console.log(`✓ 已创建备份文件: ${this.backupFile}`);
        } catch (error) {
            console.warn(`⚠ 创建备份文件失败: ${error.message}`);
        }
    }

    /**
     * 执行混淆
     */
    async obfuscate(options) {
        try {
            // 1. 解析参数
            const config = this.getObfuscationConfig(options.preset);
            
            // 2. 检查输入文件
            this.checkInputFile();
            
            // 3. 创建备份
            if (options.createBackup) {
                this.createBackup();
            }
            
            // 4. 读取源代码
            const sourceCode = fs.readFileSync(this.inputFile, 'utf8');
            console.log(`✓ 已读取源代码，行数: ${sourceCode.split('\n').length}`);
            
            // 5. 执行混淆
            console.log('🔄 开始混淆代码...');
            const startTime = Date.now();
            
            const obfuscationResult = JavaScriptObfuscator.obfuscate(sourceCode, config);
            const obfuscatedCode = obfuscationResult.getObfuscatedCode();
            
            const endTime = Date.now();
            const duration = ((endTime - startTime) / 1000).toFixed(2);
            
            // 6. 保存结果
            fs.writeFileSync(this.outputFile, obfuscatedCode, 'utf8');
            
            // 7. 显示统计信息
            this.showStatistics(sourceCode, obfuscatedCode, duration, options.preset);
            
            console.log('\n✅ 混淆完成！');
            console.log(`   输出文件: ${this.outputFile}`);
            if (options.createBackup) {
                console.log(`   备份文件: ${this.backupFile}`);
            }
            
        } catch (error) {
            console.error(`\n❌ 混淆失败: ${error.message}`);
            process.exit(1);
        }
    }

    /**
     * 显示统计信息
     */
    showStatistics(originalCode, obfuscatedCode, duration, preset) {
        const originalSize = originalCode.length;
        const obfuscatedSize = obfuscatedCode.length;
        const compressionRatio = ((obfuscatedSize / originalSize) * 100).toFixed(2);
        const originalLines = originalCode.split('\n').length;
        const obfuscatedLines = obfuscatedCode.split('\n').length;
        
        console.log('\n📊 混淆统计信息:');
        console.log(`   预设配置: ${preset}`);
        console.log(`   处理时间: ${duration}秒`);
        console.log(`   原始大小: ${(originalSize / 1024).toFixed(2)} KB (${originalLines} 行)`);
        console.log(`   混淆后大小: ${(obfuscatedSize / 1024).toFixed(2)} KB (${obfuscatedLines} 行)`);
        console.log(`   大小变化: ${compressionRatio}%`);
        
        // 安全性评估
        const securityLevel = this.assessSecurityLevel(preset);
        console.log(`   安全等级: ${securityLevel}`);
    }

    /**
     * 评估安全等级
     */
    assessSecurityLevel(preset) {
        const levels = {
            light: '🟡 低',
            medium: '🟠 中',
            high: '🔴 高',
            production: '🟠 中-高'
        };
        return levels[preset] || '🟡 未知';
    }

    /**
     * 主执行方法
     */
    async run() {
        console.log('🚀 高级JavaScript代码混淆工具启动\n');
        
        const options = this.parseArguments();
        await this.obfuscate(options);
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    const obfuscator = new AdvancedJSObfuscator();
    obfuscator.run();
}

module.exports = AdvancedJSObfuscator;
