// 注意：需要先引入CryptoJS库
// <script src="https://cdnjs.cloudflare.com/ajax/libs/crypto-js/4.1.1/crypto-js.min.js"></script>

/**
 * AES加密
 * @param {string} data 要加密的数据
 * @param {string} key 密钥（16/24/32字节）
 * @param {string} iv 初始化向量（16字节）
 * @returns {string} 加密后的Base64字符串
 */
function aesEncrypt(data, key, iv) {
    // 检查密钥和IV长度
    if (![16, 24, 32].includes(key.length)) {
        throw new Error("密钥长度必须是16、24或32字节");
    }
    if (iv.length !== 16) {
        throw new Error("初始化向量必须是16字节");
    }
    
    // 将字符串转换为WordArray对象
    const keyHex = CryptoJS.enc.Utf8.parse(key);
    const ivHex = CryptoJS.enc.Utf8.parse(iv);
    
    // 加密
    const encrypted = CryptoJS.AES.encrypt(data, keyHex, {
        iv: ivHex,
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7
    });
    
    // 返回Base64编码
    return encrypted.toString();
}

/**
 * AES解密
 * @param {string} encryptedData 加密后的Base64字符串
 * @param {string} key 密钥（16/24/32字节）
 * @param {string} iv 初始化向量（16字节）
 * @returns {string} 解密后的数据
 */
function aesDecrypt(encryptedData, key, iv) {
    if (![16, 24, 32].includes(key.length)) {
        throw new Error("密钥长度必须是16、24或32字节");
    }
    if (iv.length !== 16) {
        throw new Error("初始化向量必须是16字节");
    }
    
    const keyHex = CryptoJS.enc.Utf8.parse(key);
    const ivHex = CryptoJS.enc.Utf8.parse(iv);
    
    // 解密
    const decrypted = CryptoJS.AES.decrypt(encryptedData, keyHex, {
        iv: ivHex,
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7
    });
    
    // 将解密结果转换为字符串
    return decrypted.toString(CryptoJS.enc.Utf8);
}

// 使用示例
try {
    const data = "这是一段需要加密的数据";
    const key = "1234567890123456"; // 16字节密钥，AES-128
    const iv = "abcdefghijklmnop";  // 16字节IV
    
    // 加密
    const encrypted = aesEncrypt(data, key, iv);
    console.log("加密后:", encrypted);
    
    // 解密
    const decrypted = aesDecrypt(encrypted, key, iv);
    console.log("解密后:", decrypted);
} catch (error) {
    console.error("错误:", error.message);
}
