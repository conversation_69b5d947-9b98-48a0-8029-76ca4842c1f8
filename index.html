<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0,minimal-ui:ios" name="viewport">
    <title>{$zt_name}</title>
    <link rel="stylesheet" href="https://ztimg.hefei.cc/static/common/css/style.min.css">
    <link rel="stylesheet" href="https://ztimg.hefei.cc/static/common/css/animate.css">
    <link rel="stylesheet" href="https://ztimg.hefei.cc/static/common/css/<EMAIL>">
    <link rel="stylesheet" href="https://ztimg.hefei.cc/zt2025/lxjnc/css/index.css?v={php} echo mt_rand(1000,999999);{/php}">
    <script defer src="https://ztimg.hefei.cc/zt2025/lxjnc/css/polyfill.js?v={php} echo mt_rand(1000,999999);{/php}"></script>
    <style>
        [v-cloak] {
            display: none;
        }
    </style>
</head>

<body>
    <div id="app" class="warp" v-cloak>
        <div class="musicbtn" :class="{ on:on }"  @click="bgClick"></div>
        <div class="page loadingbg fc" :class="{blur:show}" v-if="page===0">
            <img src="https://ztimg.hefei.cc/zt2025/lxjnc/img/people.gif?v=16" class="loading">
        </div>
        <div class="page fc" :class="{blur:show}" v-if="page===1">
            <img src="https://ztimg.hefei.cc/zt2025/lxjnc/img/logo.png?v=16" class="logo">
            <img src="https://ztimg.hefei.cc/zt2025/lxjnc/img/start.png?v=16" class="start pulsate-bck2" @click="start">
            <div class="button_container">
                <img src="https://ztimg.hefei.cc/zt2025/lxjnc/img/button2.png?v=16" class="button animate__animated animate__fadeInLeft" @click="show=1">
                <img src="https://ztimg.hefei.cc/zt2025/lxjnc/img/button3.png?v=16" class="button animate__animated animate__fadeInRight" @click="show=2">
            </div>
            <div class="tip">*免费菜品券为指定菜品电子券</div>
        </div>
        <!-- 找食材页面 -->
        <div class="page page0 fc" :class="{blur:show}" v-if="page===2">
            <img src="https://ztimg.hefei.cc/zt2025/lxjnc/img/home.jpg?v=16" class="home">
            <img src="https://ztimg.hefei.cc/zt2025/lxjnc/img/zu.gif?v=16" class="zu">
            <img src="https://ztimg.hefei.cc/zt2025/lxjnc/img/tu.gif?v=16" class="tu">
            <img src="https://ztimg.hefei.cc/zt2025/lxjnc/img/yu.gif?v=16" class="yu">
            <img src="https://ztimg.hefei.cc/zt2025/lxjnc/img/people.png?v=16" class="people">
            <div class="light" :class="'light'+(item)" v-for="item in 6">
                <div class="sparkle"></div>
                <div class="sparkle"></div>
                <div class="sparkle"></div>
                <div class="sparkle"></div>
            </div>
            <div class="toucharea" @click="handleAreaClick">
                <div
                    class="touchblock"
                    v-for="(item,index) in foodList"
                    :class="'touchblock'+(index+1)"
                    @click="findFood(index, $event)"
                    ></div>
                <!-- 提示图片显示 -->
                <img
                    v-for="tip in tipMessages"
                    :key="tip.id"
                    :src="tip.src"
                    class="tip-image"
                    :style="{left: tip.x + 'px', top: tip.y + 'px'}"
                    @animationend="() => removeTip(tip.id)"
                />
                <!-- 掉落食材显示 -->
                <img
                    v-for="food in fallingFoods"
                    :key="food.id"
                    :src="'https://ztimg.hefei.cc/zt2025/lxjnc/img/food'+(food.foodIndex+1)+'.png?v=16'"
                    class="falling-food"
                    :style="{
                        left: food.currentX - 25 + 'px',
                        top: food.currentY - 25 + 'px',
                        transform: 'rotate(' + (food.progress * 360) + 'deg)'
                    }"
                />
            </div>
            <div class="time">{{findingTimeFormat}}</div>
            <div class="menu">
                <div class="plate" v-for="(item,index) in foodList" :class="{found: item.find}">
                    <img :src="'https://ztimg.hefei.cc/zt2025/lxjnc/img/food'+(index+1)+'.png?v=16'" class="food" :style="{filter: item.find ? '' : 'brightness(0) invert(36%) sepia(13%) saturate(5253%) hue-rotate(118deg) brightness(91%) contrast(101%)'}">
                    <div class="text">{{item.name}}</div>
                </div>
            </div>
            <!-- 下滑提示 -->
            <div class="scroll-hint">
                <div class="scroll-hint-text">下滑更精彩</div>
                <div class="scroll-hint-arrow">↓</div>
            </div>
        </div>
        <!-- 炒菜页面 -->
        <div class="page page2 fc" :class="{blur:show}" v-if="page===3">
            <img src="https://ztimg.hefei.cc/zt2025/lxjnc/img/bj2.jpg?v=16" class="bj2">
            <div class="time2">{{cookingTimeFormat}}</div>
            <canvas ref="gameCanvas" class="canvas" width="750" height="1334"></canvas>
            <canvas ref="foodCanvas" class="canvas food-canvas" width="750" height="1334"></canvas>
            <img ref="startButton" src="https://ztimg.hefei.cc/zt2025/lxjnc/img/button10.png?v=16" class="button10" @click="handleStartClick">
        </div>
        <!-- 活动规则弹窗 -->
        <transition name="van-fade">
            <div class="mask fc" v-if="show===1">
                <div class="popup popup1">
                    <div class="stit">游戏规则</div>
                    <div class="rule" v-html="startData.rule"></div>
                    <img src="https://ztimg.hefei.cc/zt2025/lxjnc/img/button4.png?v=16" class="back" @click="show=0">
                    <div class="close" @click="show=0"></div>
                </div>
            </div>
        </transition>
        <!-- 我的奖品弹窗 -->
        <transition name="van-fade">
            <div class="mask fc" v-if="show===2">
                <div class="popup popup2">
                    <div class="stit">我的奖品</div>
                    <img src="https://ztimg.hefei.cc/zt2025/lxjnc/img/prize1.png?v=16" class="prize" v-if="startData.prize.includes('金秋板栗烧鸡免费尝鲜券')">
                    <img src="https://ztimg.hefei.cc/zt2025/lxjnc/img/prize2.png?v=16" class="prize" v-if="startData.prize.includes('山药莲子鸡汤/莲藕鸡汤(二选一)免费尝鲜券')">
                    <img src="https://ztimg.hefei.cc/zt2025/lxjnc/img/prize3.png?v=16" class="prize" v-if="startData.prize.includes('金汤酸菜鱼免费尝鲜券')">
                    <img src="https://ztimg.hefei.cc/zt2025/lxjnc/img/prize4.png?v=16" class="prize" v-if="startData.prize.includes('指定菜品85折尝鲜券')">
                    <img src="https://ztimg.hefei.cc/zt2025/lxjnc/img/prize5.png?v=16" class="prize" v-if="startData.prize.includes('满25元减5元优惠券')">
                    <div class="button5" v-if="startData.prize" @click="goPrize(link)">
                        <wx-open-launch-weapp v-if="scene===2" id="launch-btn" appid="wxb3115b10899d5d26" :path="link" style="height: 100%;width: 100%;display:block;position: absolute;">    
                            <component :is="'script'" type="text/wxtag-template">
                                <button class="btn" style="width: 400px;height:800px;background-color: #fff;opacity: 0;">去抽奖</button>
                            </component>
                        </wx-open-launch-weapp>
                    </div>
                    <div class="no_prize" v-if="!startData.prize">你暂时还没有获得奖品，<br> 快去参加挑战吧！</div>
                    <div class="button6" v-if="!startData.prize" @click="show=0"></div>
                    <div class="close" @click="show=0"></div>
                </div>
            </div>
        </transition>
        <!-- 游戏开始提示弹窗 -->
        <transition name="van-fade">
            <div class="mask fc" v-if="show===3" @click="show=0">
                <div class="popup popup3"></div>
            </div>
        </transition>
        <!-- 游戏开始提示弹窗 -->
        <transition name="van-fade">
            <div class="mask fc" v-if="show===4">
                <div class="popup popup4">
                    <img src="https://ztimg.hefei.cc/zt2025/lxjnc/img/button7.png?v=16" class="back" @click="gamestart">
                </div>
            </div>
        </transition>
        <!-- 找齐食材弹窗 -->
        <transition name="van-fade">
            <div class="mask fc" v-if="show===5">
                <div class="popup popup5">
                    <img src="https://ztimg.hefei.cc/zt2025/lxjnc/img/button8.png?v=16" class="back2" @click="next">
                </div>
            </div>
        </transition>
        <!-- 未找齐食材弹窗 -->
        <transition name="van-fade">
            <div class="mask fc" v-if="show===6">
                <div class="popup popup6">
                    <img src="https://ztimg.hefei.cc/zt2025/lxjnc/img/button9.png?v=16" class="back2" @click="reload(2)">
                </div>
            </div>
        </transition>
        <!-- 炒菜提示弹窗 -->
        <transition name="van-fade">
            <div class="mask fc" v-if="show===7">
                <div class="popup popup7">
                    <img src="https://ztimg.hefei.cc/zt2025/lxjnc/img/button11.png?v=16" class="back2" @click="startCookingTimer">
                </div>
            </div>
        </transition>
        <!-- 炒菜成功弹窗 -->
        <transition name="van-fade">
            <div class="mask fc" v-if="show===8">
                <div class="popup popup8">
                    <div class="p1">获得【{{prize}}】</div>
                    <div class="p2">香迷糊了！快去领取奖品吧！ </div>
                    <div class="button12 back2" :class="{button12_1:prize.includes('山药莲子鸡汤/莲藕鸡汤(二选一)免费尝鲜券')}" @click="goPrize(link)">
                        <wx-open-launch-weapp v-if="scene===2" id="launch-btn" appid="wxb3115b10899d5d26" :path="link" style="height: 100%;width: 100%;display:block;position: absolute;">
                            <component :is="'script'" type="text/wxtag-template">
                                <button class="btn" style="width: 400px;height:800px;background-color: #fff;opacity: 0;">去抽奖</button>
                            </component>
                        </wx-open-launch-weapp>
                    </div>
                </div>
            </div>
        </transition>
        <!-- 炒菜失败弹窗 -->
        <transition name="van-fade">
            <div class="mask fc" v-if="show===9">
                <div class="popup popup9">
                    <img src="https://ztimg.hefei.cc/zt2025/lxjnc/img/button13.png?v=16" class="back2" @click="reload(3)">
                </div>
            </div>
        </transition>
        <!-- 已经中过奖弹窗 -->
        <transition name="van-fade">
            <div class="mask fc" v-if="show===10">
                <div class="popup popup10">
                    <img src="https://ztimg.hefei.cc/zt2025/lxjnc/img/button14.png?v=16" class="back2 button14" @click="reload(101)">
                    <img src="https://ztimg.hefei.cc/zt2025/lxjnc/img/hand.png?v=16" class="hand">
                </div>
            </div>
        </transition>

    </div>
    
    <!-- <script src="https://ztimg.hefei.cc/static/common/js/libs/vue.global.js"></script> -->
    <script src="https://ztimg.hefei.cc/static/common/js/libs/<EMAIL>"></script>
    <script src="https://ztimg.hefei.cc/static/common/js/libs/<EMAIL>"></script>
    <script src="https://ztimg.hefei.cc/static/common/js/libs/jquery-3.6.0.min.js"></script>
    <script src="https://ztimg.hefei.cc/static/common/js/libs/howler.min.js"></script>
    <script src="https://ztimg.hefei.cc/static/common/js/libs/preloadjs.min.js"></script>
    <script src="https://ztimg.hefei.cc/static/common/js/libs/dayjs.min.js"></script>
    <script src="https://ztimg.hefei.cc/static/common/js/libs/aes.js"></script>
    <script src="https://ztimg.hefei.cc/static/common/js/hook/hook1.js?v=16"></script>
    <script src="https://ztimg.hefei.cc/static/common/js/hook/hook2.js"></script>
    <script>
        var startData = {
            endtime: '{$endtime}',
            jihui: '{$jihui}',
            prize: '{$prize}',
            link: '{$link}',
            rule:`{$zt_rule|raw}`,
        }
    </script>
    <script src="main.obfuscated.js?v={php} echo mt_rand(1000,999999);{/php}"></script>
    {include file="share"/}
</body>

</html>