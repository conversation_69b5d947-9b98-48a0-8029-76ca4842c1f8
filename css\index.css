@font-face {
  font-family: '思源宋体';
  src: url(https://ztimg.hefei.cc/static/common/fonts/思源宋体.otf);
}
@font-face {
  font-family: 'Uranus_Pixel_11Px';
  src: url(https://ztimg.hefei.cc/static/common/fonts/Uranus_Pixel_11Px.ttf);
}
body,
div,
h1,
h2,
h3,
h4,
h5,
h6,
html,
li,
p,
span {
  font-size: 3.5vw;
}
.musicbtn {
  width: 4vw;
  height: 4vw;
  top: 2vw;
  right: 0.5vw;
  z-index: 11;
}
.warp {
  margin: 0 auto;
  min-height: 170vw;
  height: 100vh;
  width: 100vw;
  font-family: '思源宋体';
  background: #008042;
}
.warp .page {
  width: 100%;
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  z-index: 2;
  background: url(../img/bj.jpg?v=15) no-repeat center center / 100% auto;
  color: #352219;
}
.warp .page .logo {
  width: 20.6667vw;
  position: absolute;
  top: 2vw;
  left: 2vw;
}
.warp .page .start {
  width: 56.8vw;
  position: absolute;
  bottom: 12vh;
  transform: translateY(50%);
}
.warp .page .tip {
  color: #fff;
  font-size: 5vw;
  position: absolute;
  bottom: 2vh;
  transform: translateY(50%) scale(0.5);
  text-align: center;
}
.warp .page .button_container {
  display: flex;
  justify-content: center;
  position: absolute;
  bottom: 7vh;
  transform: translateY(50%);
}
.warp .page .button_container .button {
  width: 30.4vw;
  margin: 0 2vw;
}
.warp .page .home {
  width: 100%;
  flex-shrink: 0;
}
.warp .page .time {
  width: 22.4vw;
  height: 13.0667vw;
  background: url(../img/time.png?v=15) no-repeat center center / 100% 100%;
  position: fixed;
  top: 10vw;
  display: flex;
  justify-content: center;
  align-items: center;
  font-family: 'Uranus_Pixel_11Px';
  color: #f0f0f0;
  font-weight: bold;
  font-size: 6vw;
  letter-spacing: 0.3vw;
  padding-left: 1.3vw;
  padding-bottom: 1vw;
}
.warp .page .time2 {
  width: 22.4vw;
  height: 13.0667vw;
  background: url(../img/time.png?v=15) no-repeat center center / 100% 100%;
  position: fixed;
  top: 10vw;
  display: flex;
  justify-content: center;
  align-items: center;
  font-family: 'Uranus_Pixel_11Px';
  color: #f0f0f0;
  font-weight: bold;
  font-size: 6vw;
  letter-spacing: 0.3vw;
  padding-left: 1.3vw;
  padding-bottom: 1vw;
  transform: scale(1.5);
}
.warp .page .menu {
  width: 97.3333vw;
  height: 16.1333vw;
  background: url(../img/menu.png?v=15) no-repeat center center / 100% 100%;
  position: fixed;
  bottom: 7vw;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 9vw;
}
.warp .page .menu .plate {
  width: 8.9333vw;
  height: 7.6vw;
  background: url(../img/plate.png?v=15) no-repeat center center / 100% 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  flex-direction: column;
  margin-top: -4vw;
}
.warp .page .menu .plate .text {
  font-size: 5vw;
  position: absolute;
  bottom: -5vw;
  color: #007f43;
  color: #000;
  transform: scale(0.5);
  white-space: nowrap;
}
.warp .page .menu .plate .food {
  width: 7vw;
  height: 5vw;
  object-fit: contain;
  transition: all 0.3s ease;
}
.warp .page .menu .plate.found {
  animation: plateFound 0.8s ease-out;
}
.warp .page .menu .plate.found .food {
  animation: foodFound 1.2s ease-out;
}
.warp .page .menu .plate.found::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 12vw;
  height: 12vw;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(255, 215, 0, 0.3) 0%, rgba(255, 255, 255, 0.2) 50%, transparent 70%);
  animation: haloEffect 1.5s ease-out;
  pointer-events: none;
  z-index: -1;
}
.warp .page .menu .plate.found::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 2px;
  height: 2px;
  background: #fff;
  border-radius: 50%;
  box-shadow: 0 0 6px #fff, 0 0 12px #fff, 0 0 18px #ffd700, 0 0 24px #ffd700;
  animation: sparkleEffect 1s ease-out;
  pointer-events: none;
}
.warp .page .scroll-hint {
  position: fixed;
  bottom: 24vw;
  z-index: 20;
  pointer-events: none;
  display: flex;
  align-items: center;
}
.warp .page .scroll-hint .scroll-hint-text {
  font-size: 5vw;
  color: #fff;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
  margin-bottom: 2vw;
  font-weight: bold;
}
.warp .page .scroll-hint .scroll-hint-arrow {
  margin-left: 1vw;
  margin-top: 0vw;
  font-size: 5vw;
  color: #fff;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
  animation: scrollArrowBounce 1s ease-in-out infinite;
}
.warp .page .zu {
  width: 59.6vw;
  position: absolute;
  bottom: 0;
  left: 0;
}
.warp .page .tu {
  width: 67.467vw;
  position: absolute;
  top: 28.8vw;
  left: 0;
}
.warp .page .yu {
  width: 69.3333vw;
  position: absolute;
  top: 88vw;
  right: 0;
}
.warp .page .people {
  width: 57.2vw;
  position: absolute;
  top: 125vw;
  left: 0;
}
.warp .page .light {
  width: 6vw;
  height: 6vw;
  position: absolute;
  pointer-events: none;
  animation: rotate 40s linear infinite;
  /* 中心亮点 */
  /* 四个闪光点 */
}
.warp .page .light::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 1.5vw;
  height: 1.5vw;
  border-radius: 50%;
  background: #ffffff;
  box-shadow: 0 0 1vw rgba(255, 255, 255, 0.8), 0 0 2vw rgba(255, 215, 0, 0.6);
  animation: centerGlow 1.5s ease-in-out infinite;
}
.warp .page .light .sparkle {
  position: absolute;
  width: 0.8vw;
  height: 0.8vw;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
}
.warp .page .light .sparkle:nth-child(1) {
  top: -1vw;
  left: 50%;
  transform: translateX(-50%);
  animation: sparkle1 2.5s ease-in-out infinite;
}
.warp .page .light .sparkle:nth-child(2) {
  top: 50%;
  right: -1vw;
  transform: translateY(-50%);
  animation: sparkle2 2.5s ease-in-out infinite 0.6s;
}
.warp .page .light .sparkle:nth-child(3) {
  bottom: -1vw;
  left: 50%;
  transform: translateX(-50%);
  animation: sparkle3 2.5s ease-in-out infinite 1.2s;
}
.warp .page .light .sparkle:nth-child(4) {
  top: 50%;
  left: -1vw;
  transform: translateY(-50%);
  animation: sparkle4 2.5s ease-in-out infinite 1.8s;
}
.warp .page .light1 {
  top: 74vw;
  left: 25vw;
}
.warp .page .light2 {
  top: 85vw;
  left: 9vw;
}
.warp .page .light3 {
  top: 155vw;
  left: 18vw;
}
.warp .page .light4 {
  top: 187vw;
  left: 68vw;
}
.warp .page .light5 {
  top: 186vw;
  left: 15vw;
}
.warp .page .light6 {
  top: 252vw;
  left: 25vw;
}
.warp .page .toucharea {
  width: 100%;
  height: 100%;
  position: absolute;
}
.warp .page .toucharea .touchblock {
  width: 10vw;
  height: 10vw;
  background-color: rgba(255, 255, 255, 0);
  position: absolute;
  top: 10vw;
  left: 10vw;
}
.warp .page .toucharea .touchblock1 {
  width: 10vw;
  height: 10vw;
  top: 70vw;
  left: 19vw;
}
.warp .page .toucharea .touchblock2 {
  width: 14vw;
  height: 14vw;
  top: 75vw;
  left: 32vw;
}
.warp .page .toucharea .touchblock3 {
  width: 21vw;
  height: 18vw;
  top: 106vw;
  left: 79vw;
}
.warp .page .toucharea .touchblock4 {
  width: 23vw;
  height: 18vw;
  top: 120vw;
  left: 58vw;
}
.warp .page .toucharea .touchblock5 {
  width: 15vw;
  height: 15vw;
  top: 145vw;
  left: 12vw;
}
.warp .page .toucharea .touchblock6 {
  width: 26vw;
  height: 49vw;
  top: 237vw;
  left: 74vw;
}
.warp .page .toucharea .touchblock7 {
  width: 42vw;
  height: 51vw;
  top: 286vw;
  left: 58vw;
}
.warp .page .toucharea .touchblock8 {
  width: 50vw;
  height: 77vw;
  top: 312vw;
  left: 0vw;
}
.warp .page .toucharea .tip-image {
  height: 10vw;
  width: auto;
  position: absolute;
  z-index: 15;
  pointer-events: none;
  animation: tipShow 1.5s ease-out forwards;
  transform-origin: center center;
}
.warp .page0 {
  background: none;
  height: auto;
}
.warp .page2 {
  background: #82858f no-repeat center center / 100% 100%;
  min-height: 170vw;
}
.warp .page2 .bj2 {
  width: 100vw;
  position: absolute;
  margin-top: 4vw;
}
.warp .page2 .canvas {
  margin-top: 27vw;
  width: 100vw;
  height: 177.8667vw;
  background: transparent;
  cursor: pointer;
  position: absolute;
}
.warp .page2 .button10 {
  width: 40vw;
  position: absolute;
  left: 50%;
  bottom: 6vh;
  transform: translate(-50%, 50%);
}
.warp .loadingbg {
  background: url(../img/loading.jpg?v=15) no-repeat center center / 100% 100%;
}
.warp .loadingbg .loading {
  margin-top: 31vw;
  width: 50vw;
}
.blur {
  filter: blur(1vw);
}
.fc {
  justify-content: center;
}
.mask {
  z-index: 10;
  position: fixed;
  top: 0;
  left: 0;
  min-height: 100vh;
  width: 100vw;
  display: flex;
  flex-direction: column;
  align-items: center;
  background: rgba(18, 45, 29, 0.3);
  transform: translateX(-50%);
  left: 50%;
  color: #352219;
  font-weight: 300;
}
.mask .popup {
  margin-top: -1vw;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
}
.mask .popup .stit {
  width: 41.2vw;
  height: 10vw;
  background: url(../img/stit.png?v=15) no-repeat center top / 100% 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 5.4667vw;
  color: #fff;
  font-weight: bold;
  flex-shrink: 0;
}
.mask .popup .back {
  width: 47.3333vw;
  position: absolute;
  bottom: 6vw;
}
.mask .popup .back2 {
  width: 47.3333vw;
  position: absolute;
  bottom: 9vw;
}
.mask .popup .button12 {
  width: 47.3333vw;
  height: 14.4vw;
  background: url(../img/button12.png?v=15) no-repeat center top / 100% 100%;
  position: absolute;
  bottom: 9vw;
  overflow: hidden;
}
.mask .popup .button12_1 {
  width: 47.3333vw;
  height: 14.4vw;
  background: url(../img/button12.png?v=15) no-repeat center top / 100% 100%;
  position: absolute;
  bottom: 6vw;
  overflow: hidden;
}
.mask .popup .close {
  width: 12.2667vw;
  height: 11.7333vw;
  background: url(../img/close.png?v=15) no-repeat center top / 100% 100%;
  position: absolute;
  top: 2vw;
  right: 0vw;
}
.mask .popup1 {
  width: 88.4vw;
  height: 146.8vw;
  background: url(../img/area.png?v=15) no-repeat center top / 100% 100%;
  justify-content: flex-start;
  padding: 18vw 0vw 4vw;
}
.mask .popup1 .rule {
  margin: 2vw 6vw 20vw;
  height: 100%;
  overflow-y: auto;
  text-align: justify;
  line-height: 1.8;
  font-size: 4.1333vw;
}
.mask .popup1 .rule div {
  font-size: 4.1333vw;
}
.mask .popup1 .rule span {
  font-size: 4.1333vw;
}
.mask .popup1 .rule p {
  font-size: 4.1333vw;
}
.mask .popup2 {
  width: 88.4vw;
  height: 92.9333vw;
  background: url(../img/area2.png?v=15) no-repeat center top / 100% 100%;
  justify-content: flex-start;
  padding: 18vw 0vw 4vw;
}
.mask .popup2 .prize {
  margin-top: 6vw;
  width: 72.8vw;
  height: 27.2vw;
}
.mask .popup2 .no_prize {
  margin-top: 6vw;
  width: 72.8vw;
  height: 27.2vw;
  color: #007f43;
  font-size: 5.6vw;
  font-weight: bold;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  font-feature-settings: "halt";
  line-height: 1.6;
}
.mask .popup2 .button5 {
  width: 47.3333vw;
  height: 14.4vw;
  background: url(../img/button5.png?v=15) no-repeat center top / 100% 100%;
  position: absolute;
  bottom: 12vw;
  overflow: hidden;
}
.mask .popup2 .button6 {
  width: 47.3333vw;
  height: 14.4vw;
  background: url(../img/button6.png?v=15) no-repeat center top / 100% 100%;
  position: absolute;
  bottom: 12vw;
  overflow: hidden;
}
.mask .popup3 {
  width: 88.4vw;
  height: 115.3333vw;
  background: url(../img/area3.png?v=15) no-repeat center top / 100% 100%;
  justify-content: flex-start;
}
.mask .popup4 {
  width: 88.4vw;
  height: 104.6667vw;
  background: url(../img/area4.png?v=15) no-repeat center top / 100% 100%;
  justify-content: flex-start;
}
.mask .popup4 .back {
  width: 47.3333vw;
  position: absolute;
  bottom: 9vw;
  margin-right: 4vw;
}
.mask .popup5 {
  width: 88.4vw;
  height: 99.4667vw;
  background: url(../img/area5.png?v=15) no-repeat center top / 100% 100%;
  justify-content: flex-start;
}
.mask .popup6 {
  width: 88.4vw;
  height: 99.4667vw;
  background: url(../img/area6.png?v=15) no-repeat center top / 100% 100%;
  justify-content: flex-start;
}
.mask .popup7 {
  width: 88.4vw;
  height: 104.6667vw;
  background: url(../img/area7.png?v=15) no-repeat center top / 100% 100%;
  justify-content: flex-start;
}
.mask .popup8 {
  width: 88.4vw;
  height: 115.6vw;
  background: url(../img/area8.png?v=17) no-repeat center top / 100% 100%;
  justify-content: flex-start;
  padding-top: 63vw;
}
.mask .popup8 .p1 {
  color: #007f43;
  font-size: 4.8vw;
  font-weight: bold;
  font-feature-settings: "halt";
  max-width: 75vw;
  text-align: center;
}
.mask .popup8 .p2 {
  margin-top: 2vw;
  color: #007f43;
  font-size: 4.133vw;
  font-weight: bold;
  font-feature-settings: "halt";
}
.mask .popup8 .button12 {
  bottom: 20vw;
}
.mask .popup9 {
  width: 88.4vw;
  height: 99.4667vw;
  background: url(../img/area9.png?v=15) no-repeat center top / 100% 100%;
  justify-content: flex-start;
}
.mask .popup10 {
  width: 88.4vw;
  height: 99.4667vw;
  background: url(../img/area10.png?v=15) no-repeat center top / 100% 100%;
  justify-content: flex-start;
}
.mask .popup10 .button14 {
  animation: button-pulse 1.5s ease-in-out infinite;
}
.mask .popup10 .hand {
  width: 11vw;
  position: absolute;
  bottom: 7vw;
  right: 18vw;
  pointer-events: none;
  animation: hand-point 0.8s ease-in-out infinite;
}
@keyframes elem3 {
  from {
    transform: scale(4);
    opacity: 0;
  }
  to {
    transform: scale(1.2);
    opacity: 1;
  }
}
.elem3 {
  animation: elem3 0.6s ease both;
}
@keyframes tipShow {
  0% {
    transform: scale(0) rotate(0deg);
    opacity: 0;
  }
  20% {
    transform: scale(1.2) rotate(5deg);
    opacity: 1;
  }
  40% {
    transform: scale(1) rotate(-3deg);
    opacity: 1;
  }
  60% {
    transform: scale(1.1) rotate(2deg);
    opacity: 1;
  }
  80% {
    transform: scale(1) rotate(0deg);
    opacity: 1;
  }
  100% {
    transform: scale(0.8);
    opacity: 0;
  }
}
@keyframes centerGlow {
  0% {
    opacity: 0.8;
    transform: translate(-50%, -50%) scale(1);
    box-shadow: 0 0 1vw rgba(255, 255, 255, 0.6), 0 0 2vw rgba(255, 215, 0, 0.4);
  }
  50% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1.2);
    box-shadow: 0 0 1.5vw #ffffff, 0 0 3vw rgba(255, 215, 0, 0.8);
  }
  100% {
    opacity: 0.8;
    transform: translate(-50%, -50%) scale(1);
    box-shadow: 0 0 1vw rgba(255, 255, 255, 0.6), 0 0 2vw rgba(255, 215, 0, 0.4);
  }
}
@keyframes sparkle1 {
  0%,
  80% {
    opacity: 0;
    transform: translateX(-50%) scale(0);
  }
  10%,
  70% {
    opacity: 1;
    transform: translateX(-50%) scale(1.5);
  }
  40% {
    opacity: 0.5;
    transform: translateX(-50%) scale(1);
  }
}
@keyframes sparkle2 {
  0%,
  80% {
    opacity: 0;
    transform: translateY(-50%) scale(0);
  }
  10%,
  70% {
    opacity: 1;
    transform: translateY(-50%) scale(1.5);
  }
  40% {
    opacity: 0.5;
    transform: translateY(-50%) scale(1);
  }
}
@keyframes sparkle3 {
  0%,
  80% {
    opacity: 0;
    transform: translateX(-50%) scale(0);
  }
  10%,
  70% {
    opacity: 1;
    transform: translateX(-50%) scale(1.5);
  }
  40% {
    opacity: 0.5;
    transform: translateX(-50%) scale(1);
  }
}
@keyframes sparkle4 {
  0%,
  80% {
    opacity: 0;
    transform: translateY(-50%) scale(0);
  }
  10%,
  70% {
    opacity: 1;
    transform: translateY(-50%) scale(1.5);
  }
  40% {
    opacity: 0.5;
    transform: translateY(-50%) scale(1);
  }
}
@keyframes rotate {
  0% {
    transform: translate(-50%, -50%) rotate(0deg);
  }
  100% {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}
@keyframes plateFound {
  0% {
    transform: scale(1);
  }
  20% {
    transform: scale(1.15);
  }
  40% {
    transform: scale(0.95);
  }
  60% {
    transform: scale(1.05);
  }
  80% {
    transform: scale(0.98);
  }
  100% {
    transform: scale(1);
  }
}
@keyframes foodFound {
  0% {
    transform: scale(1) rotate(0deg);
    filter: brightness(1);
  }
  15% {
    transform: scale(1.3) rotate(5deg);
    filter: brightness(1.3) saturate(1.5);
  }
  30% {
    transform: scale(1.1) rotate(-3deg);
    filter: brightness(1.2) saturate(1.3);
  }
  45% {
    transform: scale(1.2) rotate(2deg);
    filter: brightness(1.25) saturate(1.4);
  }
  60% {
    transform: scale(1.05) rotate(-1deg);
    filter: brightness(1.1) saturate(1.2);
  }
  80% {
    transform: scale(1.08) rotate(0deg);
    filter: brightness(1.15) saturate(1.25);
  }
  100% {
    transform: scale(1) rotate(0deg);
    filter: brightness(1.1) saturate(1.2);
  }
}
@keyframes haloEffect {
  0% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.5);
  }
  30% {
    opacity: 0.8;
    transform: translate(-50%, -50%) scale(1.2);
  }
  70% {
    opacity: 0.4;
    transform: translate(-50%, -50%) scale(1.5);
  }
  100% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(2);
  }
}
@keyframes sparkleEffect {
  0% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0);
  }
  20% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
  40% {
    opacity: 0.8;
    transform: translate(-50%, -50%) scale(1.5);
  }
  60% {
    opacity: 0.6;
    transform: translate(-50%, -50%) scale(2);
  }
  80% {
    opacity: 0.3;
    transform: translate(-50%, -50%) scale(2.5);
  }
  100% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(3);
  }
}
.food-canvas {
  pointer-events: none;
  /* 让点击事件穿透到下层canvas */
}
/* 食材动画效果 */
@keyframes foodToPot {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.8;
  }
  100% {
    transform: scale(0.8);
    opacity: 1;
  }
}
/* 掉落食材样式 */
.falling-food {
  position: fixed;
  width: 50px;
  height: 50px;
  object-fit: contain;
  z-index: 1000;
  pointer-events: none;
  transition: all 0.1s ease-out;
}
@keyframes scrollArrowBounce {
  0%,
  100% {
    transform: translateY(-2vw);
  }
  50% {
    transform: translateY(0);
  }
}
strong {
  font-weight: bold !important;
}
/* Button14 放大缩小动效 */
@keyframes button-pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}
/* Hand 双击动效 */
@keyframes hand-point {
  0% {
    transform: scale(1);
  }
  10% {
    transform: scale(0.9);
  }
  20% {
    transform: scale(1);
  }
  30% {
    transform: scale(0.9);
  }
  40% {
    transform: scale(1);
  }
  100% {
    transform: scale(1);
  }
}
