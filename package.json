{"name": "js-obfuscator-tool", "version": "1.0.0", "description": "JavaScript代码混淆工具，用于混淆main.js文件", "main": "obfuscate.js", "scripts": {"obfuscate": "node obfuscate.js", "obfuscate-light": "node obfuscate-advanced.js --preset light", "obfuscate-medium": "node obfuscate-advanced.js --preset medium", "obfuscate-high": "node obfuscate-advanced.js --preset high", "obfuscate-production": "node obfuscate-advanced.js --preset production", "install-deps": "npm install", "help": "node obfuscate-advanced.js --help"}, "keywords": ["javascript", "obfuscation", "code-protection", "minification", "security"], "author": "Your Name", "license": "MIT", "dependencies": {"javascript-obfuscator": "^4.0.2"}, "devDependencies": {}, "engines": {"node": ">=12.0.0"}, "repository": {"type": "git", "url": "https://github.com/yourusername/js-obfuscator-tool.git"}, "bugs": {"url": "https://github.com/yourusername/js-obfuscator-tool/issues"}, "homepage": "https://github.com/yourusername/js-obfuscator-tool#readme"}