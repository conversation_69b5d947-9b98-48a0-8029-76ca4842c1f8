const fs = require('fs');
const path = require('path');
const JavaScriptObfuscator = require('javascript-obfuscator');

/**
 * JavaScript代码混淆工具
 * 使用javascript-obfuscator库对main.js进行混淆
 */
class JSObfuscator {
    constructor() {
        this.inputFile = 'main.js';
        this.outputFile = 'main.obfuscated.js';
        this.backupFile = 'main.backup.js';
        
        // 混淆配置选项
        this.obfuscationOptions = {
            // 基础混淆选项
            compact: true,                    // 压缩代码
            controlFlowFlattening: true,      // 控制流平坦化
            controlFlowFlatteningThreshold: 0.75,
            deadCodeInjection: true,          // 注入死代码
            deadCodeInjectionThreshold: 0.4,
            debugProtection: false,           // 调试保护（可能影响正常运行）
            debugProtectionInterval: 0,
            disableConsoleOutput: false,      // 禁用console输出
            
            // 标识符混淆
            identifierNamesGenerator: 'hexadecimal', // 标识符名称生成器
            identifiersPrefix: '',            // 标识符前缀
            renameGlobals: false,            // 重命名全局变量（可能破坏代码）
            renameProperties: false,         // 重命名属性（可能破坏代码）
            
            // 字符串混淆
            stringArray: true,               // 字符串数组
            stringArrayCallsTransform: true, // 字符串数组调用转换
            stringArrayEncoding: ['base64'], // 字符串编码方式
            stringArrayIndexShift: true,     // 字符串数组索引偏移
            stringArrayRotate: true,         // 字符串数组旋转
            stringArrayShuffle: true,        // 字符串数组洗牌
            stringArrayWrappersCount: 2,     // 字符串数组包装器数量
            stringArrayWrappersChainedCalls: true,
            stringArrayWrappersParametersMaxCount: 4,
            stringArrayWrappersType: 'function',
            stringArrayThreshold: 0.75,      // 字符串数组阈值
            
            // 变换选项
            transformObjectKeys: true,       // 转换对象键
            unicodeEscapeSequence: false,    // Unicode转义序列
            
            // 其他选项
            selfDefending: false,            // 自我防护（可能影响调试）
            seed: 0,                        // 随机种子（0表示随机）
            sourceMap: false,               // 不生成source map
            sourceMapBaseUrl: '',
            sourceMapFileName: '',
            sourceMapMode: 'separate',
            splitStrings: true,             // 分割字符串
            splitStringsChunkLength: 10,    // 分割字符串块长度
            target: 'browser'               // 目标环境
        };
    }

    /**
     * 检查输入文件是否存在
     */
    checkInputFile() {
        if (!fs.existsSync(this.inputFile)) {
            throw new Error(`输入文件 ${this.inputFile} 不存在！`);
        }
        console.log(`✓ 找到输入文件: ${this.inputFile}`);
    }

    /**
     * 创建备份文件
     */
    createBackup() {
        try {
            fs.copyFileSync(this.inputFile, this.backupFile);
            console.log(`✓ 已创建备份文件: ${this.backupFile}`);
        } catch (error) {
            console.warn(`⚠ 创建备份文件失败: ${error.message}`);
        }
    }

    /**
     * 读取源代码
     */
    readSourceCode() {
        try {
            const sourceCode = fs.readFileSync(this.inputFile, 'utf8');
            console.log(`✓ 已读取源代码，文件大小: ${(sourceCode.length / 1024).toFixed(2)} KB`);
            return sourceCode;
        } catch (error) {
            throw new Error(`读取源文件失败: ${error.message}`);
        }
    }

    /**
     * 执行混淆
     */
    obfuscateCode(sourceCode) {
        console.log('🔄 开始混淆代码...');
        const startTime = Date.now();
        
        try {
            const obfuscationResult = JavaScriptObfuscator.obfuscate(sourceCode, this.obfuscationOptions);
            const obfuscatedCode = obfuscationResult.getObfuscatedCode();
            
            const endTime = Date.now();
            const duration = ((endTime - startTime) / 1000).toFixed(2);
            
            console.log(`✓ 混淆完成，耗时: ${duration}秒`);
            console.log(`✓ 混淆后代码大小: ${(obfuscatedCode.length / 1024).toFixed(2)} KB`);
            
            return obfuscatedCode;
        } catch (error) {
            throw new Error(`代码混淆失败: ${error.message}`);
        }
    }

    /**
     * 保存混淆后的代码
     */
    saveObfuscatedCode(obfuscatedCode) {
        try {
            fs.writeFileSync(this.outputFile, obfuscatedCode, 'utf8');
            console.log(`✓ 混淆后的代码已保存到: ${this.outputFile}`);
        } catch (error) {
            throw new Error(`保存混淆代码失败: ${error.message}`);
        }
    }

    /**
     * 显示统计信息
     */
    showStatistics(originalCode, obfuscatedCode) {
        const originalSize = originalCode.length;
        const obfuscatedSize = obfuscatedCode.length;
        const compressionRatio = ((obfuscatedSize / originalSize) * 100).toFixed(2);
        
        console.log('\n📊 混淆统计信息:');
        console.log(`   原始文件大小: ${(originalSize / 1024).toFixed(2)} KB`);
        console.log(`   混淆后大小: ${(obfuscatedSize / 1024).toFixed(2)} KB`);
        console.log(`   大小比例: ${compressionRatio}%`);
        
        // 计算原始代码行数
        const originalLines = originalCode.split('\n').length;
        const obfuscatedLines = obfuscatedCode.split('\n').length;
        console.log(`   原始行数: ${originalLines}`);
        console.log(`   混淆后行数: ${obfuscatedLines}`);
    }

    /**
     * 主执行方法
     */
    async run() {
        console.log('🚀 JavaScript代码混淆工具启动\n');
        
        try {
            // 1. 检查输入文件
            this.checkInputFile();
            
            // 2. 创建备份
            // this.createBackup();
            
            // 3. 读取源代码
            const sourceCode = this.readSourceCode();
            
            // 4. 执行混淆
            const obfuscatedCode = this.obfuscateCode(sourceCode);
            
            // 5. 保存混淆后的代码
            this.saveObfuscatedCode(obfuscatedCode);
            
            // 6. 显示统计信息
            this.showStatistics(sourceCode, obfuscatedCode);
            
            console.log('\n✅ 混淆完成！');
            console.log(`   输出文件: ${this.outputFile}`);
            // console.log(`   备份文件: ${this.backupFile}`);
            
        } catch (error) {
            console.error(`\n❌ 混淆失败: ${error.message}`);
            process.exit(1);
        }
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    const obfuscator = new JSObfuscator();
    obfuscator.run();
}

module.exports = JSObfuscator;
