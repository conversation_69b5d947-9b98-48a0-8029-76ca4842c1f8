const { createApp, ref, watch, nextTick, computed } = Vue

function drawImageWithGreenFilter(ctx, image, x, y, width, height) {
    // 所有浏览器都使用像素处理方式
    // 先在临时canvas上绘制图像
    const tempCanvas = document.createElement('canvas')
    const tempCtx = tempCanvas.getContext('2d')
    tempCanvas.width = width
    tempCanvas.height = height

    // 绘制原始图像到临时canvas
    tempCtx.drawImage(image, 0, 0, width, height)

    try {
        // 获取图像数据
        const imageData = tempCtx.getImageData(0, 0, width, height)
        const data = imageData.data

        // #007f43 的RGB值
        const targetR = 0
        const targetG = 127
        const targetB = 67

        // 处理每个像素点
        for (let i = 0; i < data.length; i += 4) {
            const alpha = data[i + 3]

            // 只要像素不透明（alpha > 0），就修改RGB分量为 #007f43，保持原始alpha值
            if (alpha > 0) {
                data[i] = targetR      // 红色分量
                data[i + 1] = targetG  // 绿色分量
                data[i + 2] = targetB  // 蓝色分量
                // 保持原始alpha值，不修改 data[i + 3]
            }
        }

        // 将处理后的数据放回临时canvas
        tempCtx.putImageData(imageData, 0, 0)

        // 将处理后的图像绘制到目标canvas
        ctx.drawImage(tempCanvas, x, y)
    } catch (e) {
        // 如果像素处理失败，直接绘制 #007f43 颜色矩形
        ctx.save()
        ctx.fillStyle = '#007f43'
        ctx.fillRect(x, y, width, height)
        ctx.restore()
    }
}
function aesDecrypt(encryptedData, key = 'fre3832hdf33hdfd', iv = 'abcdefghijklmnop') {
    if (![16, 24, 32].includes(key.length)) {
        throw new Error("密钥长度必须是16、24或32字节");
    }
    if (iv.length !== 16) {
        throw new Error("初始化向量必须是16字节");
    }

    const keyHex = CryptoJS.enc.Utf8.parse(key);
    const ivHex = CryptoJS.enc.Utf8.parse(iv);

    // 解密
    const decrypted = CryptoJS.AES.decrypt(encryptedData, keyHex, {
        iv: ivHex,
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7
    });

    // 将解密结果转换为字符串
    return decrypted.toString(CryptoJS.enc.Utf8);
}

const app = createApp({
    setup() {
        const useBgMusic2 = (src) => {
            // 背景音乐
            let isIOS = !!navigator.userAgent.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/)
            let isWX = navigator.userAgent.toLowerCase().includes('micromessenger')
            let bgMusic

            // 从sessionStorage获取之前的播放状态
            const lastPlayingState = sessionStorage.getItem('bgMusicPlaying') === 'true'
            const lastPosition = parseFloat(sessionStorage.getItem('bgMusicPosition') || '0')

            let hasRestoredPositionA = false  // bgMusicA 的位置恢复标记
            let hasRestoredPositionB = false  // bgMusicB 的位置恢复标记

            bgMusicA = new Howl({
                src,
                preload: true,
                autoplay: lastPlayingState,
                loop: true,
                html5: true,
                volume: 0.5,
                onplay: () => {
                    // 只在第一次播放时恢复位置
                    if (lastPosition > 0 && !hasRestoredPositionA) {
                        bgMusicA.seek(lastPosition)
                        hasRestoredPositionA = true
                    }
                }
            })

            bgMusicB = new Howl({
                src,
                preload: true,
                loop: true,
                volume: 0.5,
                onplay: () => {
                    // 只在第一次播放时恢复位置
                    if (lastPosition > 0 && !hasRestoredPositionB) {
                        bgMusicB.seek(lastPosition)
                        hasRestoredPositionB = true
                    }
                }
            })

            if (!isWX) {
                bgMusic = bgMusicA
            } else {
                document.addEventListener("WeixinJSBridgeReady", function () {
                    WeixinJSBridge.invoke('getNetworkType', {}, function (e) {
                        if (!bgMusicA.playing()) {
                            console.log(1);
                            bgMusicA.play();
                        }
                        if (!bgMusicA.playing() || !isIOS) {
                            console.log(bgMusicA.playing());
                            bgMusicA.unload()
                            bgMusicB.play();
                            bgMusic = bgMusicB
                        } else {
                            console.log(3);
                            bgMusicB.unload()
                            bgMusic = bgMusicA
                        }
                    });
                }, false);
            }

            const on = Vue.ref(true)

            // 在页面卸载时保存播放状态和位置
            window.addEventListener('beforeunload', () => {
                sessionStorage.setItem('bgMusicPlaying', bgMusic.playing())
                sessionStorage.setItem('bgMusicPosition', bgMusic.seek())
            })

            const bgClick = () => {
                if (bgMusic.playing()) {
                    on.value = false
                    bgMusic.pause()
                    sessionStorage.setItem('bgMusicPlaying', 'false')
                } else {
                    on.value = true
                    bgMusic.play()
                    sessionStorage.setItem('bgMusicPlaying', 'true')
                }
            }

            return {
                on,
                bgClick
            }
        }
        const { on, bgClick } = useBgMusic2('https://ztimg.hefei.cc/zt2025/lxjnc/123.mp3')//调用景音乐
        const page = ref(1)
        const show = ref(0)

        // 食材组合信息提示相关 - 使用数组管理多个提示框
        const ingredientTooltips = ref([])
        let tooltipIdCounter = 0

        // 生成唯一的提示框ID
        const generateTooltipId = () => {
            return ++tooltipIdCounter
        }

        // 添加 ref 声明
        const gameCanvas = ref(null)
        const foodCanvas = ref(null)
        const startButton = ref(null)
        const { endtime } = startData
        const opportunity = ref((+startData.jihui))
        const start = () => {
            if (endtime === '1') return vantAlert('活动未开始')
            if (endtime === '2') return vantAlert('活动已结束')
            if (opportunity.value >= 1) {
                page.value = 2
                show.value = 4
                opportunity.value--
            } else {
                vantAlert('您今日的机会已用完')
            }
        }
        // 刷新页面功能
        const { reload, savePage, deletePage } = useReload()
        if (savePage) {
            page.value = +savePage
            deletePage()
            if (page.value === 2) {
                show.value = 4
            } else if (page.value === 3) {
                show.value = 7
                page.value = 3
                nextTick(() => {
                    initCookingGame()
                })
                defaultHttp('gamestart', {}, { status: 1 })
            } else if (page.value === 101) {
                page.value = 1
                show.value = 2
            } else {
                page.value = 1
            }
        }
        // 判断是否是初次进入网页,执行预加载
        const imageList = [
            'https://ztimg.hefei.cc/zt2025/lxjnc/img/area.png?v=16',
            'https://ztimg.hefei.cc/zt2025/lxjnc/img/area2.png?v=16',
            'https://ztimg.hefei.cc/zt2025/lxjnc/img/area3.png?v=16',
            'https://ztimg.hefei.cc/zt2025/lxjnc/img/area4.png?v=16',
            'https://ztimg.hefei.cc/zt2025/lxjnc/img/area5.png?v=16',
            'https://ztimg.hefei.cc/zt2025/lxjnc/img/area6.png?v=16',
            'https://ztimg.hefei.cc/zt2025/lxjnc/img/area7.png?v=16',
            'https://ztimg.hefei.cc/zt2025/lxjnc/img/area8.png?v=16',
            'https://ztimg.hefei.cc/zt2025/lxjnc/img/area9.png?v=16',
            'https://ztimg.hefei.cc/zt2025/lxjnc/img/bj.jpg?v=16',
            'https://ztimg.hefei.cc/zt2025/lxjnc/img/bj2.jpg?v=16',
            'https://ztimg.hefei.cc/zt2025/lxjnc/img/box.png?v=16',
            'https://ztimg.hefei.cc/zt2025/lxjnc/img/button10.png?v=16',
            'https://ztimg.hefei.cc/zt2025/lxjnc/img/button11.png?v=16',
            'https://ztimg.hefei.cc/zt2025/lxjnc/img/button12.png?v=16',
            'https://ztimg.hefei.cc/zt2025/lxjnc/img/button13.png?v=16',
            'https://ztimg.hefei.cc/zt2025/lxjnc/img/button2.png?v=16',
            'https://ztimg.hefei.cc/zt2025/lxjnc/img/button3.png?v=16',
            'https://ztimg.hefei.cc/zt2025/lxjnc/img/button4.png?v=16',
            'https://ztimg.hefei.cc/zt2025/lxjnc/img/button5.png?v=16',
            'https://ztimg.hefei.cc/zt2025/lxjnc/img/button6.png?v=16',
            'https://ztimg.hefei.cc/zt2025/lxjnc/img/button7.png?v=16',
            'https://ztimg.hefei.cc/zt2025/lxjnc/img/button8.png?v=16',
            'https://ztimg.hefei.cc/zt2025/lxjnc/img/button9.png?v=16',
            'https://ztimg.hefei.cc/zt2025/lxjnc/img/close.png?v=16',
            'https://ztimg.hefei.cc/zt2025/lxjnc/img/dishes1.png?v=16',
            'https://ztimg.hefei.cc/zt2025/lxjnc/img/dishes2.png?v=16',
            'https://ztimg.hefei.cc/zt2025/lxjnc/img/dishes3.png?v=16',
            'https://ztimg.hefei.cc/zt2025/lxjnc/img/errtip1.png?v=16',
            'https://ztimg.hefei.cc/zt2025/lxjnc/img/errtip2.png?v=16',
            'https://ztimg.hefei.cc/zt2025/lxjnc/img/errtip3.png?v=16',
            'https://ztimg.hefei.cc/zt2025/lxjnc/img/errtip4.png?v=16',
            'https://ztimg.hefei.cc/zt2025/lxjnc/img/errtip5.png?v=16',
            'https://ztimg.hefei.cc/zt2025/lxjnc/img/errtip6.png?v=16',
            'https://ztimg.hefei.cc/zt2025/lxjnc/img/errtip7.png?v=16',
            'https://ztimg.hefei.cc/zt2025/lxjnc/img/food1.png?v=16',
            'https://ztimg.hefei.cc/zt2025/lxjnc/img/food2.png?v=16',
            'https://ztimg.hefei.cc/zt2025/lxjnc/img/food3.png?v=16',
            'https://ztimg.hefei.cc/zt2025/lxjnc/img/food4.png?v=16',
            'https://ztimg.hefei.cc/zt2025/lxjnc/img/food5.png?v=16',
            'https://ztimg.hefei.cc/zt2025/lxjnc/img/food6.png?v=16',
            'https://ztimg.hefei.cc/zt2025/lxjnc/img/food7.png?v=16',
            'https://ztimg.hefei.cc/zt2025/lxjnc/img/food7_1.png?v=16',
            'https://ztimg.hefei.cc/zt2025/lxjnc/img/food8.png?v=16',
            'https://ztimg.hefei.cc/zt2025/lxjnc/img/home.jpg?v=16',
            'https://ztimg.hefei.cc/zt2025/lxjnc/img/logo.png?v=16',
            'https://ztimg.hefei.cc/zt2025/lxjnc/img/logo2.png?v=16',
            'https://ztimg.hefei.cc/zt2025/lxjnc/img/main.png?v=16',
            'https://ztimg.hefei.cc/zt2025/lxjnc/img/menu.png?v=16',
            'https://ztimg.hefei.cc/zt2025/lxjnc/img/people.png?v=16',
            'https://ztimg.hefei.cc/zt2025/lxjnc/img/plate.png?v=16',
            'https://ztimg.hefei.cc/zt2025/lxjnc/img/prize1.png?v=16',
            'https://ztimg.hefei.cc/zt2025/lxjnc/img/prize2.png?v=16',
            'https://ztimg.hefei.cc/zt2025/lxjnc/img/prize3.png?v=16',
            'https://ztimg.hefei.cc/zt2025/lxjnc/img/prize4.png?v=16',
            'https://ztimg.hefei.cc/zt2025/lxjnc/img/prize5.png?v=16',
            'https://ztimg.hefei.cc/zt2025/lxjnc/img/start.png?v=16',
            'https://ztimg.hefei.cc/zt2025/lxjnc/img/stit.png?v=16',
            'https://ztimg.hefei.cc/zt2025/lxjnc/img/time.png?v=16',
            'https://ztimg.hefei.cc/zt2025/lxjnc/img/tip1.png?v=16',
            'https://ztimg.hefei.cc/zt2025/lxjnc/img/tip2.png?v=16',
            'https://ztimg.hefei.cc/zt2025/lxjnc/img/tip3.png?v=16',
            'https://ztimg.hefei.cc/zt2025/lxjnc/img/tip4.png?v=16',
            'https://ztimg.hefei.cc/zt2025/lxjnc/img/tip5.png?v=16',
            'https://ztimg.hefei.cc/zt2025/lxjnc/img/tip6.png?v=16',
            'https://ztimg.hefei.cc/zt2025/lxjnc/img/tip7.png?v=16',
            'https://ztimg.hefei.cc/zt2025/lxjnc/img/tip8.png?v=16',
            'https://ztimg.hefei.cc/zt2025/lxjnc/img/tu.gif?v=16',
            'https://ztimg.hefei.cc/zt2025/lxjnc/img/yu.gif?v=16',
            'https://ztimg.hefei.cc/zt2025/lxjnc/img/zu.gif?v=16',
            'https://ztimg.hefei.cc/zt2025/lxjnc/main.mp4?v=16',
            'https://ztimg.hefei.cc/zt2025/lxjnc/main2.mp4?v=16',
        ];
        const { loadStart, progress, progressShow, startFlag } = cheackStartPopup(imageList)
        loadStart()
        if (startFlag) {
            page.value = 0
        }
        watch(progress, (newVal) => {
            if (newVal === 100) {
                if (startFlag) {
                    page.value = 1
                    show.value = 3
                }
            }
        })
        // 找食材页面倒计时逻辑
        const findingTime = ref(60)
        const findingTimeFormat = computed(() => {
            return dayjs(findingTime.value * 1000).format('mm:ss')
        })

        watch(findingTime, (newVal) => {
            if (newVal === 0) {
                // 找食材时间到，显示失败弹窗
                show.value = 6
                gameEnd(false)
                // 清除倒计时
                if (findingTimer) {
                    clearInterval(findingTimer)
                    findingTimer = null
                }
            }
        })
        let findingTimer = null

        // 启动找食材倒计时
        const startFindingTimer = () => {
            // 清除之前的计时器（如果存在）
            if (findingTimer) {
                clearInterval(findingTimer)
            }

            // 重置倒计时
            findingTime.value = 60
            show.value = 0

            // 启动新的计时器
            findingTimer = setInterval(() => {
                if (findingTime.value > 0) {
                    findingTime.value--
                }
            }, 1000)
        }

        // 停止找食材倒计时
        const stopFindingTimer = () => {
            if (findingTimer) {
                clearInterval(findingTimer)
                findingTimer = null
            }
        }

        const gamestart = () => {
            defaultHttp('gamestart', {}, { status: 1 })
            startFindingTimer()
        }

        // 炒菜页面倒计时逻辑
        const cookingTime = ref(60)
        const cookingTimeFormat = computed(() => {
            return dayjs(cookingTime.value * 1000).format('mm:ss')
        })
        watch(cookingTime, (newVal) => {
            if (newVal === 0) {
                // 炒菜时间到，显示失败弹窗
                vantAlert('炒菜失败', () => {
                    reload(3)
                })
                gameEnd(false)
                // 停止炒菜
                if (isVideoPlaying) {
                    endCooking()
                }
                // 清除倒计时
                if (cookingTimer) {
                    clearInterval(cookingTimer)
                    cookingTimer = null
                }
            }
        })
        let cookingTimer = null

        // 启动炒菜倒计时
        const startCookingTimer = () => {
            // 清除之前的计时器（如果存在）
            if (cookingTimer) {
                clearInterval(cookingTimer)
            }

            // 重置倒计时
            cookingTime.value = 60
            show.value = 0

            // 启动新的计时器
            cookingTimer = setInterval(() => {
                if (cookingTime.value > 0) {
                    cookingTime.value--
                }
            }, 1000)
        }

        // 停止炒菜倒计时
        const stopCookingTimer = () => {
            if (cookingTimer) {
                clearInterval(cookingTimer)
                cookingTimer = null
            }
        }

        const foodList = ref([
            { name: '酸菜', find: false },
            { name: '山药', find: false },
            { name: '莲子', find: false },
            { name: '鱼', find: false },
            { name: '鸡', find: false },
            { name: '红枣', find: false },
            { name: '板栗', find: false },
            { name: '笋子', find: false },
        ])
        watch(foodList, (newVal) => {
            if (newVal.every(item => item.find)) {
                // 找齐所有食材，停止倒计时
                stopFindingTimer()
                show.value = 5
            }
        }, { deep: true })

        // 提示图片消息数组
        const tipMessages = ref([])

        // 掉落食材动画数组
        const fallingFoods = ref([])

        // 生成唯一ID
        let tipIdCounter = 0
        const generateTipId = () => {
            return ++tipIdCounter
        }

        // 移除指定的tip
        const removeTip = (tipId) => {
            const index = tipMessages.value.findIndex(tip => tip.id === tipId)
            if (index !== -1) {
                tipMessages.value.splice(index, 1)
            }
        }

        // 移除指定的掉落食材
        const removeFallingFood = (foodId) => {
            const index = fallingFoods.value.findIndex(food => food.id === foodId)
            if (index !== -1) {
                fallingFoods.value.splice(index, 1)
            }
        }

        // 创建掉落食材动画
        const createFallingFoodAnimation = (foodIndex, startX, startY) => {
            // 计算目标位置（对应的plate位置）
            const menu = document.querySelector('.menu')
            const plates = menu.querySelectorAll('.plate')
            const targetPlate = plates[foodIndex]
            const plateRect = targetPlate.getBoundingClientRect()

            const targetX = plateRect.left + plateRect.width / 2
            const targetY = plateRect.top + plateRect.height / 2

            // 创建掉落食材对象
            const fallingFood = {
                id: generateTipId(),
                foodIndex: foodIndex,
                startX: startX,
                startY: startY,
                targetX: targetX,
                targetY: targetY,
                currentX: startX,
                currentY: startY,
                progress: 0,
                duration: 600, // 动画持续时间600ms
                startTime: Date.now()
            }

            fallingFoods.value.push(fallingFood)

            // 开始动画
            animateFallingFood(fallingFood.id)
        }

        // 执行掉落食材动画
        const animateFallingFood = (foodId) => {
            const food = fallingFoods.value.find(f => f.id === foodId)
            if (!food) return

            const currentTime = Date.now()
            const elapsed = currentTime - food.startTime
            food.progress = Math.min(elapsed / food.duration, 1)

            // 使用抛物线轨迹（重力效果）
            const easeProgress = food.progress
            const gravityProgress = food.progress * food.progress // 重力加速度效果

            // 计算当前位置
            food.currentX = food.startX + (food.targetX - food.startX) * easeProgress
            food.currentY = food.startY + (food.targetY - food.startY) * gravityProgress

            if (food.progress < 1) {
                // 继续动画
                requestAnimationFrame(() => animateFallingFood(foodId))
            } else {
                // 动画完成，移除掉落食材
                removeFallingFood(foodId)
            }
        }

        // 显示提示图片
        const displayTip = (x, y, isCorrect, foodIndex = null) => {
            let tipSrc = ''
            if (isCorrect && foodIndex !== null) {
                // 找到正确食材，显示对应的tip图片
                tipSrc = `https://ztimg.hefei.cc/zt2025/lxjnc/img/tip${foodIndex + 1}.png?v=16`
            } else {
                // 点错了，随机显示errtip图片
                const randomIndex = Math.floor(Math.random() * 5) + 1
                tipSrc = `https://ztimg.hefei.cc/zt2025/lxjnc/img/errtip${randomIndex}.png?v=16`
            }

            // 生成唯一ID
            const tipId = generateTipId()

            // 使用new Image预加载图片并获取真实尺寸
            const img = new Image()
            img.onload = () => {
                // 图片加载完成，获取真实尺寸
                const vw = window.innerWidth / 100
                const margin = 2 * vw // 距离屏幕边缘至少2vw

                // 计算图片在10vw高度下的实际宽度
                const targetHeight = 10 * vw
                const aspectRatio = img.naturalWidth / img.naturalHeight
                const targetWidth = targetHeight * aspectRatio

                // 获取父容器(.toucharea)的位置信息
                const touchArea = document.querySelector('.toucharea')
                const touchAreaRect = touchArea.getBoundingClientRect()

                // 将屏幕坐标转换为相对于父容器的坐标
                const relativeX = x - touchAreaRect.left
                const relativeY = y - touchAreaRect.top

                // 计算理想位置（以点击位置为中心，相对于父容器）
                let idealX = relativeX - targetWidth / 2
                let idealY = relativeY - targetHeight / 2

                // 计算边界（相对于父容器）
                const minX = margin
                const minY = margin
                const maxX = touchAreaRect.width - targetWidth - margin
                const maxY = touchAreaRect.height - targetHeight - margin

                // 限制在安全范围内
                let finalX = Math.max(minX, Math.min(idealX, maxX))
                let finalY = Math.max(minY, Math.min(idealY, maxY)) - 30

                // 添加到消息数组
                tipMessages.value.push({
                    id: tipId,
                    src: tipSrc,
                    x: finalX,
                    y: finalY
                })
            }

            img.onerror = () => {
                // 图片加载失败，使用默认位置
                const vw = window.innerWidth / 100
                const margin = 2 * vw
                tipMessages.value.push({
                    id: tipId,
                    src: tipSrc,
                    x: margin,
                    y: margin
                })
            }

            // 开始加载图片
            img.src = tipSrc
        }

        const findFood = (index, event) => {
            event.stopPropagation() // 阻止事件冒泡
            const rect = event.target.getBoundingClientRect()
            const x = rect.left + rect.width / 2
            const y = rect.top + rect.height / 2
            // 如果已经找到了，就不进行处理
            if (foodList.value[index].find) {
                return
            }
            // 创建掉落食材动画
            createFallingFoodAnimation(index, x, y)

            // 延迟设置食材为找到状态，让动画先开始
            setTimeout(() => {
                foodList.value[index].find = true
            }, 300) // 动画进行到一半时才亮起plate中的food

            displayTip(x, y, true, index)
        }

        // 处理区域点击（点击空白区域显示错误提示）
        const handleAreaClick = (event) => {
            // 如果点击的是touchblock，不处理
            if (event.target.classList.contains('touchblock')) {
                return
            }

            const x = event.clientX
            const y = event.clientY

            // 显示错误提示
            displayTip(x, y, false)
        }

        // 跳转到下一页（炒菜游戏）
        const next = () => {
            show.value = 7
            page.value = 3
            nextTick(() => {
                initCookingGame()
            })
        }
        // 炒菜游戏相关变量
        let canvas = null // 主canvas（视频处理）
        let ctx = null
        let foodCanvasEl = null // 食材canvas
        let foodCtx = null

        let isGameEnded = false // 游戏是否已经结束
        let video = null
        let mainImage = null
        let isVideoPlaying = false
        let isCooking = false // 是否正在炒菜状态
        let foodImages = [] // 存储食材图片
        let foodInPot = [] // 存储进入锅中的食材状态
        const potCenter = { x: 375, y: 800 } // 锅的中心点
        const potRadius = 60 // 锅的半径，用于分散食材位置

        // 预定义的锅内位置偏移（相对于锅中心的偏移）
        const potPositionOffsets = [
            { x: 0, y: -10 },      // 中心位置
            { x: -25, y: -15 },    // 左上
            { x: 25, y: -15 },     // 右上
            { x: -30, y: 10 },     // 左下
            { x: 30, y: 10 },      // 右下
            { x: -15, y: -30 },    // 上左
            { x: 15, y: -30 },     // 上右
            { x: -40, y: -5 },     // 左中
            { x: 40, y: -5 },      // 右中
            { x: 0, y: 20 }        // 下中
        ]
        // 盒子和菜品图片相关变量
        let boxImage = null // 盒子图片
        let dishImages = [] // 菜品图片数组
        let boxImageLoaded = false
        let dishImagesLoaded = false
        // 菜品状态：控制是否显示绿色滤镜
        let dishStates = [
            { isGreen: true }, // 金秋板栗烧鸡
            { isGreen: true }, // 金汤酸菜鱼
            { isGreen: true }  // 山药莲子鸡汤
        ]
        // 食材位置配置数组 (x,y,width都为像素值，aspectRatio动态获取)
        const foodPositions = [
            { x: 180, y: 740, width: 120, aspectRatio: 1.0, name: '酸菜' },
            { x: 160, y: 850, width: 120, aspectRatio: 1.0, name: '山药' },
            { x: 165, y: 980, width: 120, aspectRatio: 1.0, name: '莲子' },
            { x: 300, y: 1030, width: 140, aspectRatio: 1.0, name: '鱼' },
            { x: 450, y: 1030, width: 120, aspectRatio: 1.0, name: '鸡' },
            { x: 595, y: 980, width: 120, aspectRatio: 1.0, name: '红枣' },
            { x: 600, y: 850, width: 120, aspectRatio: 1.0, name: '板栗' },
            { x: 570, y: 740, width: 100, aspectRatio: 1.0, name: '笋子' }
        ]

        // 初始化炒菜游戏
        const initCookingGame = () => {
            // 使用 ref 获取canvas元素和上下文
            canvas = gameCanvas.value
            foodCanvasEl = foodCanvas.value

            if (!canvas || !foodCanvasEl) {
                console.error('Canvas元素未找到')
                return
            }

            ctx = canvas.getContext('2d')
            foodCtx = foodCanvasEl.getContext('2d')

            // 设置食材canvas背景透明
            foodCanvasEl.style.backgroundColor = 'transparent'





            // 创建video元素
            video = document.createElement('video')
            video.src = 'https://ztimg.hefei.cc/zt2025/lxjnc/main.mp4?v=16'
            video.loop = false
            video.muted = true // 静音以允许自动播放
            video.crossOrigin = 'anonymous'
            video.playsInline = true // 防止iOS全屏播放
            video.setAttribute('playsinline', '') // 兼容性写法
            video.setAttribute('webkit-playsinline', '') // 兼容旧版iOS

            // 创建图片元素用于初始显示
            mainImage = new Image()
            mainImage.src = 'https://ztimg.hefei.cc/zt2025/lxjnc/img/main.png?v=16'
            mainImage.crossOrigin = 'anonymous'

            // 设置canvas背景透明
            canvas.style.backgroundColor = 'transparent'

            // 游戏状态
            isVideoPlaying = false

            // 图片加载完成后绘制初始画面
            mainImage.addEventListener('load', () => {
                checkAllImagesLoaded()
            })

            // 视频加载完成但不自动播放
            video.addEventListener('loadeddata', () => {
                console.log('视频已加载完成，等待用户点击开始')
            })

            // 视频播放结束事件
            video.addEventListener('ended', () => {
                console.log('视频播放完毕，结束炒菜')
                endCooking()
            })

            // 错误处理
            video.addEventListener('error', (e) => {
                console.error('视频加载失败:', e)
            })

            // 初始化食材状态
            initFoodStates()

            // 添加canvas点击事件监听
            canvas.addEventListener('click', handleCanvasClick)

            // 添加foodCanvas点击事件监听
            canvas.addEventListener('click', handleFoodCanvasClick)

            // 加载食材图片
            loadFoodImages()

            // 加载盒子和菜品图片
            loadBoxAndDishImages()

        }

        // 加载食材图片
        const loadFoodImages = () => {
            foodImages = []
            let loadedCount = 0
            const totalImages = 9 // 8个基础食材 + 1个food7_1

            for (let i = 1; i <= 8; i++) {
                const img = new Image()
                img.crossOrigin = 'anonymous'
                img.onload = () => {
                    // 获取图片的真实宽高比并更新到配置数组
                    const aspectRatio = img.naturalWidth / img.naturalHeight
                    foodPositions[i - 1].aspectRatio = aspectRatio

                    loadedCount++
                    if (loadedCount === totalImages) {
                        // 所有食材图片和宽高比都获取完成后，重新绘制画面
                        checkAllImagesLoaded()
                    }
                }
                img.onerror = () => {
                    console.error(`食材图片 food${i}.png?v=16 加载失败`)
                    foodPositions[i - 1].aspectRatio = 1.0 // 默认宽高比
                    loadedCount++
                    if (loadedCount === totalImages) {
                        checkAllImagesLoaded()
                    }
                }
                img.src = `https://ztimg.hefei.cc/zt2025/lxjnc/img/food${i}.png?v=16`
                foodImages.push(img)
            }

            // 加载food7_1.png（用于food7进入锅内后的替换图片）
            const food7_1_img = new Image()
            food7_1_img.crossOrigin = 'anonymous'
            food7_1_img.onload = () => {
                loadedCount++
                if (loadedCount === totalImages) {
                    checkAllImagesLoaded()
                }
            }
            food7_1_img.onerror = () => {
                console.error('食材图片 food7_1.png?v=16 加载失败')
                loadedCount++
                if (loadedCount === totalImages) {
                    checkAllImagesLoaded()
                }
            }
            food7_1_img.src = 'https://ztimg.hefei.cc/zt2025/lxjnc/img/food7_1.png?v=16'
            foodImages.push(food7_1_img) // 索引8，用于food7的替换图片
        }

        // 加载盒子和菜品图片
        const loadBoxAndDishImages = () => {
            // 加载盒子图片
            boxImage = new Image()
            boxImage.crossOrigin = 'anonymous'
            boxImage.onload = () => {
                boxImageLoaded = true
                checkAllImagesLoaded()
            }
            boxImage.onerror = () => {
                boxImageLoaded = true // 即使失败也标记为已加载，避免阻塞
                checkAllImagesLoaded()
            }
            boxImage.src = 'https://ztimg.hefei.cc/zt2025/lxjnc/img/box.png?v=16'

            // 加载菜品图片
            dishImages = []
            let dishLoadedCount = 0
            const dishNames = ['dishes1.png?v=16', 'dishes2.png?v=16', 'dishes3.png?v=16']

            dishNames.forEach((dishName, index) => {
                const img = new Image()
                img.crossOrigin = 'anonymous'
                img.onload = () => {
                    dishLoadedCount++
                    if (dishLoadedCount === dishNames.length) {
                        console.log('所有菜品图片加载完成')
                        dishImagesLoaded = true
                        checkAllImagesLoaded()
                    }
                }
                img.onerror = () => {
                    console.error(`菜品图片 ${dishName} 加载失败`)
                    dishLoadedCount++
                    if (dishLoadedCount === dishNames.length) {
                        dishImagesLoaded = true
                        checkAllImagesLoaded()
                    }
                }
                img.src = `https://ztimg.hefei.cc/zt2025/lxjnc/img/${dishName}`
                dishImages.push(img)
            })
        }

        // 检查所有图片是否加载完成
        const checkAllImagesLoaded = () => {
            // 检查食材图片是否都加载完成（包括food7_1）
            const allFoodImagesLoaded = foodImages.length === 9 &&
                foodImages.every(img => img.complete && img.naturalWidth > 0)
            if (boxImageLoaded && dishImagesLoaded && mainImage.complete && allFoodImagesLoaded) {
                console.log('所有图片加载完成，开始绘制')
                drawInitialFrame()
            }
        }

        // 初始化食材状态
        const initFoodStates = () => {
            foodInPot = foodPositions.map((pos, index) => {
                // 为每个食材分配一个锅内位置偏移
                const positionOffset = potPositionOffsets[index % potPositionOffsets.length]

                return {
                    inPot: false,
                    isConsumed: false, // 新增：是否已被消耗
                    isTemporarilyHidden: false, // 新增：是否临时隐藏（用于山药莲子鸡汤）
                    isMovingToPot: false, // 新增：是否正在移动到锅中（用于food7图片切换）
                    originalX: pos.x,
                    originalY: pos.y,
                    currentX: pos.x,
                    currentY: pos.y,
                    // 锅内的固定位置（相对于锅中心的偏移）
                    potPositionX: potCenter.x + positionOffset.x,
                    potPositionY: potCenter.y + positionOffset.y - 20, // 整体往上偏移20px
                    potOffsetX: 0, // 锅铲移动时的额外偏移
                    potOffsetY: 0, // 锅铲移动时的额外偏移
                    // 动画相关属性
                    isAnimating: false,
                    animationProgress: 0,
                    animationStartTime: 0,
                    animationDuration: 400, // 动画持续时间（毫秒）
                    scale: 1,
                    opacity: 1
                }
            })
        }

        // 处理foodCanvas点击事件（盒子点击）
        const handleFoodCanvasClick = (event) => {
            const rect = foodCanvasEl.getBoundingClientRect()
            const clickX = event.clientX - rect.left
            const clickY = event.clientY - rect.top

            // 将点击坐标转换为canvas内部坐标系
            const scaleX = foodCanvasEl.width / rect.width
            const scaleY = foodCanvasEl.height / rect.height
            const canvasClickX = clickX * scaleX
            const canvasClickY = clickY * scaleY

            // 检查是否点击了盒子区域
            const boxWidth = 200
            const canvasWidth = foodCanvasEl.width
            const spacing = (canvasWidth - 3 * boxWidth) / 4
            const boxY = 100
            const boxHeight = 165 // 估算盒子高度

            for (let i = 0; i < 3; i++) {
                const boxX = spacing + i * (boxWidth + spacing)

                // 检查点击是否在盒子范围内
                if (canvasClickX >= boxX && canvasClickX <= boxX + boxWidth &&
                    canvasClickY >= boxY && canvasClickY <= boxY + boxHeight) {

                    // 显示该菜品的食材组合信息，传入盒子中心位置
                    const boxCenterX = boxX + boxWidth / 2
                    const boxCenterY = boxY + boxHeight / 2
                    showDishIngredients(i, boxCenterX, boxCenterY)
                    return
                }
            }
        }

        // 显示菜品食材组合信息提示
        const showDishIngredients = (dishIndex, clickX, clickY) => {
            if (dishIndex >= 0 && dishIndex < dishRecipes.length) {
                const recipe = dishRecipes[dishIndex]

                // 创建新的提示框对象
                const tooltipId = generateTooltipId()
                const newTooltip = {
                    id: tooltipId,
                    show: true,
                    dishName: recipe.name,
                    ingredients: [...recipe.ingredients],
                    x: clickX,
                    y: clickY,
                    alpha: 1, // 直接显示，不淡入
                    animationId: null,
                    startTime: Date.now(),
                    phase: 'show'
                }

                // 添加到提示框数组
                ingredientTooltips.value.push(newTooltip)

                // 重新绘制
                if (!isVideoPlaying) {
                    drawInitialFrame()
                }

                // 3秒后直接隐藏
                setTimeout(() => {
                    removeTooltip(tooltipId)
                }, 3000)
            }
        }

        // 移除指定的提示框
        const removeTooltip = (tooltipId) => {
            const index = ingredientTooltips.value.findIndex(t => t.id === tooltipId)
            if (index !== -1) {
                const tooltip = ingredientTooltips.value[index]
                if (tooltip.animationId) {
                    cancelAnimationFrame(tooltip.animationId)
                }
                ingredientTooltips.value.splice(index, 1)

                // 重新绘制
                if (!isVideoPlaying) {
                    drawInitialFrame()
                }
            }
        }

        // 处理canvas点击事件
        const handleCanvasClick = (event) => {

            // 炒菜时禁止点击食材
            if (isCooking) {
                console.log('炒菜中，无法点击食材')
                return
            }

            const rect = canvas.getBoundingClientRect()
            const clickX = event.clientX - rect.left
            const clickY = event.clientY - rect.top

            // 将点击坐标转换为canvas内部坐标系
            const scaleX = canvas.width / rect.width
            const scaleY = canvas.height / rect.height
            const canvasClickX = clickX * scaleX
            const canvasClickY = clickY * scaleY

            // 检查是否点击了食材
            let foundFood = false
            foodPositions.forEach((position, index) => {
                const foodState = foodInPot[index]

                // 如果食材已被消耗，则无法点击
                if (foodState && foodState.isConsumed) {
                    return
                }

                // 检查是否点击了在锅里的食材
                if (foodState && foodState.inPot && !foodState.isAnimating) {
                    // 计算锅里食材的点击区域（以当前位置为中心）
                    const drawWidth = position.width
                    const drawHeight = drawWidth / position.aspectRatio
                    const drawX = foodState.currentX - drawWidth / 2
                    const drawY = foodState.currentY - drawHeight / 2

                    // 使用转换后的canvas坐标进行检查
                    if (canvasClickX >= drawX && canvasClickX <= drawX + drawWidth &&
                        canvasClickY >= drawY && canvasClickY <= drawY + drawHeight) {

                        // 启动食材回到盘子的动画
                        startFoodReturnAnimation(index)
                        foundFood = true
                        return
                    }
                }
                // 检查是否点击了在盘子里的食材
                else if (!foodState || !foodState.inPot) {
                    const drawWidth = position.width
                    const drawHeight = drawWidth / position.aspectRatio
                    const drawX = position.x - drawWidth / 2
                    const drawY = position.y - drawHeight / 2

                    // 使用转换后的canvas坐标进行检查
                    if (canvasClickX >= drawX && canvasClickX <= drawX + drawWidth &&
                        canvasClickY >= drawY && canvasClickY <= drawY + drawHeight) {

                        // 启动食材进入锅中的动画
                        if (!foodInPot[index]) {
                            foodInPot[index] = {}
                        }

                        if (!foodInPot[index].inPot && !foodInPot[index].isAnimating) {
                            startFoodAnimation(index)
                            foundFood = true
                        }
                        return
                    }
                }
            })

            if (!foundFood) {
                console.log('没有点击到任何食材')
            }
        }

        // 启动食材动画
        const startFoodAnimation = (index) => {
            const foodState = foodInPot[index]
            foodState.isAnimating = true
            foodState.isMovingToPot = true // 标记正在移动到锅中
            foodState.animationStartTime = Date.now()
            foodState.animationProgress = 0

            // 开始动画循环
            animateFoodToPot(index)
        }

        // 启动食材回到盘子的动画
        const startFoodReturnAnimation = (index) => {
            const foodState = foodInPot[index]
            foodState.isAnimating = true
            foodState.animationStartTime = Date.now()
            foodState.animationProgress = 0

            // 开始动画循环
            animateFoodToPlate(index)
        }

        // 食材移动到锅中的动画
        const animateFoodToPot = (index) => {
            const foodState = foodInPot[index]
            const currentTime = Date.now()
            const elapsed = currentTime - foodState.animationStartTime
            const progress = Math.min(elapsed / foodState.animationDuration, 1)

            // 使用缓动函数（easeInOutQuad）
            const easeProgress = progress < 0.5
                ? 2 * progress * progress
                : 1 - Math.pow(-2 * progress + 2, 2) / 2

            // 计算当前位置（从原位置到锅内分配的位置）
            const startX = foodState.originalX
            const startY = foodState.originalY
            const endX = foodState.potPositionX
            const endY = foodState.potPositionY

            foodState.currentX = startX + (endX - startX) * easeProgress
            foodState.currentY = startY + (endY - startY) * easeProgress

            // 重新绘制
            if (!isVideoPlaying) {
                drawInitialFrame()
            }

            if (progress < 1) {
                // 继续动画
                requestAnimationFrame(() => animateFoodToPot(index))
            } else {
                // 动画完成
                foodState.isAnimating = false
                foodState.inPot = true
                foodState.isMovingToPot = false // 动画完成，已经在锅中
                foodState.scale = 1
                foodState.opacity = 1
                foodState.currentX = foodState.potPositionX
                foodState.currentY = foodState.potPositionY
            }
        }

        // 食材从锅中回到盘子的动画
        const animateFoodToPlate = (index) => {
            const foodState = foodInPot[index]
            const currentTime = Date.now()
            const elapsed = currentTime - foodState.animationStartTime
            const progress = Math.min(elapsed / foodState.animationDuration, 1)

            // 使用缓动函数（easeInOutQuad）
            const easeProgress = progress < 0.5
                ? 2 * progress * progress
                : 1 - Math.pow(-2 * progress + 2, 2) / 2

            // 计算当前位置（从锅内分配位置到原位置）
            const startX = foodState.potPositionX
            const startY = foodState.potPositionY
            const endX = foodState.originalX
            const endY = foodState.originalY

            foodState.currentX = startX + (endX - startX) * easeProgress
            foodState.currentY = startY + (endY - startY) * easeProgress

            // 重新绘制
            if (!isVideoPlaying) {
                drawInitialFrame()
            }

            if (progress < 1) {
                // 继续动画
                requestAnimationFrame(() => animateFoodToPlate(index))
            } else {
                // 动画完成
                foodState.isAnimating = false
                foodState.inPot = false
                foodState.isMovingToPot = false // 回到盘子，清除移动标志
                foodState.scale = 1
                foodState.opacity = 1
                foodState.currentX = foodState.originalX
                foodState.currentY = foodState.originalY
                // 重置锅中偏移
                foodState.potOffsetX = 0
                foodState.potOffsetY = 0
            }
        }

        // 计算绘制参数（适用于视频和图片）
        const calculateDrawParams = (sourceWidth, sourceHeight) => {
            const sourceAspectRatio = sourceWidth / sourceHeight
            const canvasAspectRatio = canvas.width / canvas.height

            let drawWidth, drawHeight, drawX, drawY

            if (sourceAspectRatio > canvasAspectRatio) {
                // 源更宽，以canvas宽度为准
                drawWidth = canvas.width
                drawHeight = canvas.width / sourceAspectRatio
                drawX = 0
                drawY = canvas.height - drawHeight // 居底
            } else {
                // 源更高，以canvas高度为准
                drawHeight = canvas.height
                drawWidth = canvas.height * sourceAspectRatio
                drawX = (canvas.width - drawWidth) / 2
                drawY = 0
            }

            return { drawWidth, drawHeight, drawX, drawY }
        }

        // 绘制食材
        const drawFoodItems = () => {
            // 检查是否所有食材的宽高比都已获取
            const allAspectRatiosLoaded = foodPositions.every(pos => pos.aspectRatio !== 1.0)

            if (!allAspectRatiosLoaded) {
                return
            }

            // 清除食材canvas
            foodCtx.clearRect(0, 0, foodCanvasEl.width, foodCanvasEl.height)

            // 绘制盒子和菜品
            drawBoxesAndDishes()

            // 只遍历前8个食材图片（不包括food7_1）
            for (let index = 0; index < 8; index++) {
                const img = foodImages[index]
                if (img && img.complete && img.naturalWidth > 0) {
                    const position = foodPositions[index]
                    const foodState = foodInPot[index]

                    // 如果食材已被消耗或临时隐藏，则不绘制
                    if (foodState.isConsumed || foodState.isTemporarilyHidden) {
                        continue
                    }

                    // 选择要绘制的图片
                    let imageToRender = img
                    let aspectRatioToUse = position.aspectRatio

                    // food7（索引6，板栗）进入锅内或正在移动到锅内时，使用food7_1图片
                    if (index === 6 && (foodState.inPot || foodState.isMovingToPot) && foodImages[8]) {
                        imageToRender = foodImages[8] // food7_1.png
                        aspectRatioToUse = imageToRender.naturalWidth / imageToRender.naturalHeight
                    }

                    // 使用像素宽度和真实宽高比计算尺寸
                    let drawWidth = position.width * foodState.scale
                    let drawHeight = (drawWidth / foodState.scale) / aspectRatioToUse * foodState.scale

                    // food6（索引5，红枣）进入锅内或正在移动到锅内时，宽高再减半
                    if (index === 5 && (foodState.inPot || foodState.isMovingToPot)) {
                        drawWidth *= 0.5
                        drawHeight *= 0.5
                    }

                    // 使用当前位置（可能是原位置或锅中位置）
                    const drawX = foodState.currentX - drawWidth / 2
                    const drawY = foodState.currentY - drawHeight / 2

                    // 应用透明度
                    foodCtx.globalAlpha = foodState.opacity

                    // 绘制食材图片到食材canvas
                    foodCtx.drawImage(imageToRender, drawX, drawY, drawWidth, drawHeight)

                    // 重置透明度
                    foodCtx.globalAlpha = 1
                }
            }
        }

        // 绘制盒子和菜品
        const drawBoxesAndDishes = () => {
            if (!boxImageLoaded || !dishImagesLoaded) {
                return
            }

            // 菜品配置：名称和对应的菜品图片宽度
            const dishConfigs = [
                { name: '金秋板栗烧鸡', dishWidth: 140 },
                { name: '金汤酸菜鱼', dishWidth: 140 },
                { name: '山药莲子鸡汤', dishWidth: 105 }
            ]

            // 盒子配置：3个盒子水平排列在foodCanvas上方
            const boxWidth = 200
            const canvasWidth = foodCanvasEl.width // 750
            const spacing = (canvasWidth - 3 * boxWidth) / 4 // 计算间距
            const boxY = 100 // 盒子距离顶部的位置

            for (let i = 0; i < 3; i++) {
                const boxX = spacing + i * (boxWidth + spacing)

                // 检查当前盒子是否有活跃的提示框
                const hasActiveTooltip = ingredientTooltips.value.some(tooltip => {
                    const tooltipBoxIndex = Math.floor((tooltip.x - spacing) / (boxWidth + spacing))
                    return tooltipBoxIndex === i && tooltip.show && tooltip.alpha > 0
                })

                // 绘制盒子
                if (boxImage && boxImage.complete && boxImage.naturalWidth > 0) {
                    // 计算盒子高度（保持宽高比）
                    const boxAspectRatio = boxImage.naturalWidth / boxImage.naturalHeight
                    const boxHeight = boxWidth / boxAspectRatio

                    foodCtx.drawImage(boxImage, boxX, boxY, boxWidth, boxHeight)

                    // 绘制文字（盒子内部上方）
                    foodCtx.fillStyle = 'white'
                    foodCtx.font = 'bold 18px Arial, sans-serif'
                    foodCtx.textAlign = 'center'
                    foodCtx.textBaseline = 'top'

                    const textX = boxX + boxWidth / 2
                    const textY = boxY + 8 // 文字在盒子内部上方，距离盒子顶部20px
                    foodCtx.fillText('点击查看提示', textX, textY)

                    // 只有在没有活跃提示框时才绘制菜品图片
                    if (!hasActiveTooltip && dishImages[i] && dishImages[i].complete && dishImages[i].naturalWidth > 0) {
                        const dishWidth = dishConfigs[i].dishWidth
                        const dishAspectRatio = dishImages[i].naturalWidth / dishImages[i].naturalHeight
                        const dishHeight = dishWidth / dishAspectRatio

                        const dishX = boxX + (boxWidth - dishWidth) / 2 // 居中对齐
                        const dishY = boxY + boxHeight - dishHeight - 20 // 盒子内部下方，距离盒子底部20px

                        // 如果需要显示绿色效果，使用兼容iOS的方法
                        if (dishStates[i].isGreen) {
                            // 使用兼容iOS的绿色滤镜效果
                            drawImageWithGreenFilter(foodCtx, dishImages[i], dishX, dishY, dishWidth, dishHeight)
                        } else {
                            // 正常显示
                            foodCtx.drawImage(dishImages[i], dishX, dishY, dishWidth, dishHeight)
                        }
                    }
                }
            }
        }

        // 绘制所有食材组合提示框
        const drawIngredientTooltip = () => {
            // 绘制所有活跃的提示框
            ingredientTooltips.value.forEach(tooltip => {
                if (!tooltip.show || tooltip.alpha <= 0) return

                drawSingleTooltip(tooltip)
            })
        }

        // 绘制单个提示框（仅显示文字提示）
        const drawSingleTooltip = (tooltip) => {
            // 保存canvas状态
            foodCtx.save()

            // 设置透明度
            foodCtx.globalAlpha = tooltip.alpha

            // 提示文字内容 - 分为两行
            const line1 = `此款产品由${tooltip.ingredients.length}种`
            const line2 = `食材搭配制作而成`

            // 设置文字样式 - 更大更美观的字体
            foodCtx.font = 'bold 20px "Microsoft YaHei", Arial, sans-serif'
            foodCtx.textAlign = 'center'
            foodCtx.textBaseline = 'middle'

            // 计算文字尺寸（取两行中较宽的）
            const textMetrics1 = foodCtx.measureText(line1)
            const textMetrics2 = foodCtx.measureText(line2)
            const textWidth = Math.max(textMetrics1.width, textMetrics2.width)
            const lineHeight = 30 // 单行文字高度
            const textHeight = lineHeight * 2 + 10 // 两行文字高度加行间距

            // // 背景参数
            // const bgPadding = 10 // 增大内边距
            // const bgWidth = textWidth + bgPadding * 2
            // const bgHeight = textHeight + bgPadding
            // const bgRadius = 15 // 圆角半径

            // // 计算背景位置（居中显示）
            // const bgX = tooltip.x - bgWidth / 2
            // const bgY = tooltip.y - bgHeight / 2

            // // 绘制背景（渐变效果）
            // const gradient = foodCtx.createLinearGradient(bgX, bgY, bgX, bgY + bgHeight)
            // gradient.addColorStop(0, 'rgba(255, 248, 220, 0.95)') // 浅米色
            // gradient.addColorStop(1, 'rgba(245, 222, 179, 0.95)') // 深一点的米色

            // foodCtx.fillStyle = gradient
            // foodCtx.strokeStyle = '#d4af37' // 金色边框
            // foodCtx.lineWidth = 3

            // // 绘制圆角矩形背景
            // foodCtx.beginPath()
            // foodCtx.moveTo(bgX + bgRadius, bgY)
            // foodCtx.lineTo(bgX + bgWidth - bgRadius, bgY)
            // foodCtx.quadraticCurveTo(bgX + bgWidth, bgY, bgX + bgWidth, bgY + bgRadius)
            // foodCtx.lineTo(bgX + bgWidth, bgY + bgHeight - bgRadius)
            // foodCtx.quadraticCurveTo(bgX + bgWidth, bgY + bgHeight, bgX + bgWidth - bgRadius, bgY + bgHeight)
            // foodCtx.lineTo(bgX + bgRadius, bgY + bgHeight)
            // foodCtx.quadraticCurveTo(bgX, bgY + bgHeight, bgX, bgY + bgHeight - bgRadius)
            // foodCtx.lineTo(bgX, bgY + bgRadius)
            // foodCtx.quadraticCurveTo(bgX, bgY, bgX + bgRadius, bgY)
            // foodCtx.closePath()
            // foodCtx.fill()
            // foodCtx.stroke()

            // // 添加内阴影效果
            // foodCtx.strokeStyle = 'rgba(212, 175, 55, 0.3)'
            // foodCtx.lineWidth = 1
            // foodCtx.beginPath()
            // foodCtx.moveTo(bgX + bgRadius + 2, bgY + 2)
            // foodCtx.lineTo(bgX + bgWidth - bgRadius - 2, bgY + 2)
            // foodCtx.quadraticCurveTo(bgX + bgWidth - 2, bgY + 2, bgX + bgWidth - 2, bgY + bgRadius + 2)
            // foodCtx.lineTo(bgX + bgWidth - 2, bgY + bgHeight - bgRadius - 2)
            // foodCtx.quadraticCurveTo(bgX + bgWidth - 2, bgY + bgHeight - 2, bgX + bgWidth - bgRadius - 2, bgY + bgHeight - 2)
            // foodCtx.lineTo(bgX + bgRadius + 2, bgY + bgHeight - 2)
            // foodCtx.quadraticCurveTo(bgX + 2, bgY + bgHeight - 2, bgX + 2, bgY + bgHeight - bgRadius - 2)
            // foodCtx.lineTo(bgX + 2, bgY + bgRadius + 2)
            // foodCtx.quadraticCurveTo(bgX + 2, bgY + 2, bgX + bgRadius + 2, bgY + 2)
            // foodCtx.closePath()
            // foodCtx.stroke()

            // 计算两行文字的Y坐标
            const line1Y = 10 + tooltip.y - lineHeight / 2 - 5 // 第一行往上偏移
            const line2Y = 10 + tooltip.y + lineHeight / 2 + 5 // 第二行往下偏移

            // 绘制第一行文字阴影
            foodCtx.fillStyle = 'rgba(255, 255, 255, 0.8)'
            foodCtx.fillText(line1, tooltip.x + 1, line1Y + 1)

            // 绘制第二行文字阴影
            foodCtx.fillText(line2, tooltip.x + 1, line2Y + 1)

            // 绘制第一行主文字（深棕色，更有质感）
            foodCtx.fillStyle = '#007f43'
            foodCtx.fillText(line1, tooltip.x, line1Y)

            // 绘制第二行主文字
            foodCtx.fillText(line2, tooltip.x, line2Y)

            // 恢复canvas状态
            foodCtx.restore()
        }

        // 切换菜品状态（从绿色到正常或从正常到绿色）
        const toggleDishState = (dishIndex) => {
            if (dishIndex >= 0 && dishIndex < dishStates.length) {
                dishStates[dishIndex].isGreen = !dishStates[dishIndex].isGreen
                // 重新绘制
                if (!isVideoPlaying) {
                    drawInitialFrame()
                }
            }
        }

        // 设置菜品为正常状态
        const setDishNormal = (dishIndex) => {
            if (dishIndex >= 0 && dishIndex < dishStates.length) {
                dishStates[dishIndex].isGreen = false
                // 重新绘制
                if (!isVideoPlaying) {
                    drawInitialFrame()
                }
            }
        }

        // 设置菜品为绿色状态
        const setDishGreen = (dishIndex) => {
            if (dishIndex >= 0 && dishIndex < dishStates.length) {
                dishStates[dishIndex].isGreen = true
                // 重新绘制
                if (!isVideoPlaying) {
                    drawInitialFrame()
                }
            }
        }

        // 更新食材位置 - 模拟真实炒菜效果（包含锅铲动作计算）
        const updateFoodPositionsWithCooking = () => {
            if (!isCooking) {
                return // 不在炒菜状态
            }

            // 计算锅铲位置 - 模拟真实炒菜动作
            if (!cookingStartTime) {
                cookingStartTime = Date.now()
            }

            const currentTime = Date.now() - cookingStartTime
            const timeInSeconds = currentTime / 1000

            // 锅的中心点
            const potCenterX = 375
            const potCenterY = 800

            // 创建复合的炒菜动作：左右摆动 + 前后推拉 + 轻微的上下颠勺

            // 主要的左右摆动（频率较快，模拟翻炒）
            const mainSwingAmplitude = 40 // 左右摆动幅度
            const mainSwingFreq = 1.5 // 每秒1.5次摆动
            const mainSwingX = Math.sin(timeInSeconds * mainSwingFreq * 2 * Math.PI) * mainSwingAmplitude

            // 前后推拉动作（频率稍慢）
            const pushPullAmplitude = 15 // 前后推拉幅度
            const pushPullFreq = 0.8 // 每秒0.8次推拉
            const pushPullY = Math.sin(timeInSeconds * pushPullFreq * 2 * Math.PI) * pushPullAmplitude

            // 轻微的颠勺动作（偶尔的上下动作）
            const tossAmplitude = 8 // 颠勺幅度
            const tossFreq = 0.3 // 每秒0.3次颠勺
            const tossY = Math.sin(timeInSeconds * tossFreq * 2 * Math.PI) * tossAmplitude

            // 添加一些随机性，让动作更自然
            const randomFactorX = Math.sin(timeInSeconds * 2.3) * 5
            const randomFactorY = Math.cos(timeInSeconds * 1.7) * 3

            // 计算锅铲当前位置
            const spatulaCenter = {
                x: potCenterX + mainSwingX + randomFactorX,
                y: potCenterY + pushPullY + tossY + randomFactorY
            }

            // 更新食材位置
            foodInPot.forEach((foodState, index) => {
                if (foodState.inPot) {
                    // 计算锅铲相对于锅中心的偏移
                    const spatulaOffsetX = spatulaCenter.x - potCenter.x
                    const spatulaOffsetY = spatulaCenter.y - potCenter.y

                    // 不同食材有不同的响应特性
                    const foodPhase = index * 0.5 // 每个食材有不同的相位差
                    const foodTime = timeInSeconds + foodPhase

                    // 基础跟随系数（不同食材跟随程度不同）
                    const baseFollowFactor = 0.3 + (index % 3) * 0.1 // 0.3-0.5之间

                    // 动态跟随系数：锅铲移动快时，食材跟随更明显
                    const spatulaSpeed = Math.abs(spatulaOffsetX) + Math.abs(spatulaOffsetY)
                    const dynamicFollowFactor = baseFollowFactor + Math.min(spatulaSpeed / 50, 0.3)

                    // 食材的独立运动（模拟在锅中翻滚）
                    const independentMotionX = Math.sin(foodTime * 1.2) * 8 + Math.cos(foodTime * 0.8) * 5
                    const independentMotionY = Math.cos(foodTime * 1.5) * 6 + Math.sin(foodTime * 0.6) * 4

                    // 受锅铲影响的运动
                    const spatulaInfluenceX = spatulaOffsetX * dynamicFollowFactor
                    const spatulaInfluenceY = spatulaOffsetY * dynamicFollowFactor

                    // 添加一些弹性效果（食材不会立即跟随锅铲）
                    const elasticity = 0.15
                    const targetOffsetX = spatulaInfluenceX + independentMotionX
                    const targetOffsetY = spatulaInfluenceY + independentMotionY

                    // 平滑过渡到目标位置
                    foodState.potOffsetX += (targetOffsetX - foodState.potOffsetX) * elasticity
                    foodState.potOffsetY += (targetOffsetY - foodState.potOffsetY) * elasticity

                    // 限制食材移动范围（不能离锅太远）
                    const maxOffset = 35
                    foodState.potOffsetX = Math.max(-maxOffset, Math.min(maxOffset, foodState.potOffsetX))
                    foodState.potOffsetY = Math.max(-maxOffset, Math.min(maxOffset, foodState.potOffsetY))

                    // 更新当前位置
                    foodState.currentX = foodState.potPositionX + foodState.potOffsetX
                    foodState.currentY = foodState.potPositionY + foodState.potOffsetY

                    // 添加轻微的旋转效果（通过缩放模拟）
                    const rotationEffect = Math.sin(foodTime * 2) * 0.05
                    foodState.scale = 1 + rotationEffect
                }
            })
        }

        // 绘制初始图片帧
        const drawInitialFrame = () => {
            // 清除主canvas
            ctx.clearRect(0, 0, canvas.width, canvas.height)

            // 计算图片绘制参数
            const drawParams = calculateDrawParams(mainImage.naturalWidth, mainImage.naturalHeight)

            // 按您指定的方式绘制图片到主canvas
            ctx.drawImage(mainImage, drawParams.drawX, drawParams.drawY, drawParams.drawWidth, drawParams.drawHeight)



            // 绘制食材到食材canvas
            drawFoodItems()

            // 绘制食材组合提示框
            drawIngredientTooltip()
        }

        // 绘制视频帧函数
        const drawFrame = () => {
            if (!isVideoPlaying || video.paused || video.ended) {
                return
            }

            // 计算视频绘制参数
            const drawParams = calculateDrawParams(video.videoWidth, video.videoHeight)

            // 清除主canvas
            // ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 绘制视频帧到主canvas
            ctx.drawImage(video, drawParams.drawX, drawParams.drawY, drawParams.drawWidth, drawParams.drawHeight)

            // 更新食材位置（包含炒菜动作）
            updateFoodPositionsWithCooking()

            // 绘制食材到食材canvas（覆盖层）
            drawFoodItems()

            // 绘制食材组合提示框
            drawIngredientTooltip()

            // 请求下一帧
            requestAnimationFrame(drawFrame)
        }

        // 根据锅中食材组合获取对应的视频源
        const getVideoSrcByIngredients = () => {
            // 获取当前锅中的食材
            const ingredientsInPot = []
            foodInPot.forEach((foodState, index) => {
                if (foodState.inPot) {
                    const ingredientName = foodPositions[index].name
                    ingredientsInPot.push(ingredientName)
                }
            })

            console.log('当前锅中的食材:', ingredientsInPot)

            // 检查是否匹配山药莲子鸡汤的配方
            const shanYaoLianZiJiTangRecipe = dishRecipes.find(recipe => recipe.name === '山药莲子鸡汤')
            if (shanYaoLianZiJiTangRecipe) {
                // 检查食材数量是否完全相等
                if (ingredientsInPot.length === shanYaoLianZiJiTangRecipe.ingredients.length) {
                    // 检查每个食材是否都匹配（考虑重复食材）
                    const recipeCopy = [...shanYaoLianZiJiTangRecipe.ingredients]
                    const potCopy = [...ingredientsInPot]

                    let isExactMatch = true
                    for (const ingredient of potCopy) {
                        const index = recipeCopy.indexOf(ingredient)
                        if (index === -1) {
                            isExactMatch = false
                            break
                        }
                        recipeCopy.splice(index, 1)
                    }

                    if (isExactMatch && recipeCopy.length === 0) {
                        console.log('匹配山药莲子鸡汤配方，使用main2.mp4')
                        return 'https://ztimg.hefei.cc/zt2025/lxjnc/main2.mp4?v=16'
                    }
                }
            }

            // 默认使用main.mp4
            console.log('使用默认视频main.mp4')
            return 'https://ztimg.hefei.cc/zt2025/lxjnc/main.mp4?v=16'
        }

        // 处理开始按钮点击
        const handleStartClick = () => {
            if (!isVideoPlaying) {
                // 检查是否有食材在锅中
                const hasIngredientsInPot = foodInPot.some(food => food && food.inPot)
                if (!hasIngredientsInPot) {
                    console.log('请先添加食材到锅中')
                    return
                }

                // 开始炒菜
                startCooking()
            } else {
                // 炒菜中不允许暂停/恢复
                console.log('炒菜中，无法暂停')
            }
        }

        // 检查当前是否在做山药莲子鸡汤
        const isCurrentlyMakingShanYaoLianZiJiTang = () => {
            // 获取当前锅中的食材
            const ingredientsInPot = []
            foodInPot.forEach((foodState, index) => {
                if (foodState.inPot) {
                    const ingredientName = foodPositions[index].name
                    ingredientsInPot.push(ingredientName)
                }
            })

            // 检查是否匹配山药莲子鸡汤的配方
            const shanYaoLianZiJiTangRecipe = dishRecipes.find(recipe => recipe.name === '山药莲子鸡汤')
            if (shanYaoLianZiJiTangRecipe) {
                // 检查食材数量是否完全相等
                if (ingredientsInPot.length === shanYaoLianZiJiTangRecipe.ingredients.length) {
                    // 检查每个食材是否都匹配（考虑重复食材）
                    const recipeCopy = [...shanYaoLianZiJiTangRecipe.ingredients]
                    const potCopy = [...ingredientsInPot]

                    let isExactMatch = true
                    for (const ingredient of potCopy) {
                        const index = recipeCopy.indexOf(ingredient)
                        if (index === -1) {
                            isExactMatch = false
                            break
                        }
                        recipeCopy.splice(index, 1)
                    }

                    return isExactMatch && recipeCopy.length === 0
                }
            }
            return false
        }

        // 开始炒菜
        const startCooking = () => {
            isVideoPlaying = true
            isCooking = true

            // 根据当前锅中的食材组合判断要做的菜品，选择对应的视频
            const videoSrc = getVideoSrcByIngredients()
            const isMakingShanYaoLianZiJiTang = isCurrentlyMakingShanYaoLianZiJiTang()

            // 如果需要切换视频源，则重新设置
            if (video.src !== videoSrc) {
                video.src = videoSrc
                console.log('切换视频源为:', videoSrc)
            }

            // 重置视频到开头
            video.currentTime = 0

            video.play().then(() => {
                console.log('开始炒菜，视频播放中')

                // 如果是山药莲子鸡汤，立即隐藏锅里的食材
                if (isMakingShanYaoLianZiJiTang) {
                    console.log('山药莲子鸡汤：隐藏锅里的食材')
                    hideFoodInPot()
                }

                drawFrame()
            }).catch((error) => {
                console.error('视频播放失败:', error)
                // 如果视频播放失败，回退到图片显示
                endCooking()
            })
        }

        // 隐藏锅里的食材（用于山药莲子鸡汤）
        const hideFoodInPot = () => {
            foodInPot.forEach((foodState, index) => {
                if (foodState.inPot) {
                    // 临时隐藏食材，但保持inPot状态为true，以便后续合成判断
                    foodState.isTemporarilyHidden = true
                }
            })
        }

        // 恢复锅里食材的显示
        const showFoodInPot = () => {
            foodInPot.forEach((foodState, index) => {
                if (foodState.inPot && foodState.isTemporarilyHidden) {
                    // 恢复显示食材
                    foodState.isTemporarilyHidden = false
                }
            })
        }

        // 结束炒菜
        const endCooking = () => {
            isVideoPlaying = false
            isCooking = false

            // 停止视频
            video.pause()
            video.currentTime = 0

            // 恢复食材显示（如果之前被隐藏了）
            showFoodInPot()

            // 检查食材组合并合成菜品
            checkAndCombineIngredients()

            // 回到静态图片
            drawInitialFrame()
        }

        // 菜品配方定义
        const dishRecipes = [
            {
                name: '金秋板栗烧鸡',
                ingredients: ['板栗', '鸡'],
                dishIndex: 0
            },
            {
                name: '金汤酸菜鱼',
                ingredients: ['鱼', '酸菜', '笋子'],
                dishIndex: 1
            },
            {
                name: '山药莲子鸡汤',
                ingredients: ['鸡', '山药', '莲子', '红枣'],
                dishIndex: 2
            }
        ]

        // 检查食材组合并合成菜品
        const checkAndCombineIngredients = () => {
            // 获取当前锅中的食材
            const ingredientsInPot = []
            foodInPot.forEach((foodState, index) => {
                if (foodState.inPot) {
                    const ingredientName = foodPositions[index].name
                    ingredientsInPot.push(ingredientName)
                }
            })

            console.log('当前锅中的食材:', ingredientsInPot)

            // 检查是否完全匹配某个配方
            let matchedRecipe = null
            for (const recipe of dishRecipes) {
                // 检查食材数量是否完全相等
                if (ingredientsInPot.length === recipe.ingredients.length) {
                    // 检查每个食材是否都匹配（考虑重复食材）
                    const recipeCopy = [...recipe.ingredients]
                    const potCopy = [...ingredientsInPot]

                    let isExactMatch = true
                    for (const ingredient of potCopy) {
                        const index = recipeCopy.indexOf(ingredient)
                        if (index === -1) {
                            isExactMatch = false
                            break
                        }
                        recipeCopy.splice(index, 1)
                    }

                    if (isExactMatch && recipeCopy.length === 0) {
                        matchedRecipe = recipe
                        break
                    }
                }
            }

            if (matchedRecipe) {
                console.log(`完全匹配配方: ${matchedRecipe.name}`)
                // 合成菜品
                combineDish(matchedRecipe)
            } else {
                console.log('食材组合不匹配任何配方，炒菜失败')
                show.value = 9
                stopCookingTimer()
                gameEnd(false)
            }
        }

        // 播放音乐
        const successSound = new Howl({ src: 'cg2.mp3', volume: 1 })
        const playMusic = () => {
            if (successSound) {
                successSound.play()
            }
        }
        // 检查胜利条件
        const checkWinCondition = () => {
            // 检查是否所有菜品都已制作完成（变为正常状态）
            const allDishesCompleted = dishStates.every(dish => !dish.isGreen)

            if (allDishesCompleted) {
                console.log('所有菜品制作完成，游戏胜利！')
                // 停止倒计时
                stopCookingTimer()
                // 显示成功弹窗
                gameEnd(true)
                // 播放成功音效
                playMusic()
            }
        }

        // 合成菜品并执行动画
        const combineDish = (recipe) => {
            // 在锅中心生成菜品图片
            const dishImage = dishImages[recipe.dishIndex]
            if (!dishImage || !dishImage.complete) {
                console.error(`菜品图片未加载: ${recipe.name}`)
                return
            }

            // 移除用于合成的食材
            removeUsedIngredients(recipe.ingredients, false, recipe.dishIndex)

            // 创建菜品动画对象
            const dishAnimation = {
                image: dishImage,
                dishIndex: recipe.dishIndex,
                currentX: potCenter.x,
                currentY: potCenter.y,
                targetX: 0, // 将在动画开始时计算
                targetY: 0, // 将在动画开始时计算
                width: 80, // 动画中的菜品尺寸
                height: 0, // 将根据宽高比计算
                isAnimating: true,
                animationProgress: 0,
                animationStartTime: Date.now(),
                animationDuration: 1000 // 1秒动画
            }

            // 计算目标位置（对应盒子的位置）
            const boxWidth = 200
            const canvasWidth = foodCanvasEl.width
            const spacing = (canvasWidth - 3 * boxWidth) / 4
            const boxY = 100
            const boxX = spacing + recipe.dishIndex * (boxWidth + spacing)

            dishAnimation.targetX = boxX + boxWidth / 2
            dishAnimation.targetY = boxY + 50 // 盒子内部位置

            // 计算菜品高度
            const aspectRatio = dishImage.naturalWidth / dishImage.naturalHeight
            dishAnimation.height = dishAnimation.width / aspectRatio

            // 开始动画
            animateDishToDrop(dishAnimation)
        }

        // 检查某个食材是否还被其他未完成的菜品需要
        const isIngredientNeededByOtherDishes = (ingredientName, excludeDishIndex = -1) => {
            // 遍历所有菜品配方
            for (const recipe of dishRecipes) {
                // 排除当前正在制作的菜品
                if (recipe.dishIndex === excludeDishIndex) {
                    continue
                }

                // 检查该菜品是否还未完成（isGreen为true表示未完成）
                if (dishStates[recipe.dishIndex].isGreen) {
                    // 检查该菜品的配方中是否包含这个食材
                    if (recipe.ingredients.includes(ingredientName)) {
                        return true
                    }
                }
            }
            return false
        }

        // 移除或恢复食材
        const removeUsedIngredients = (usedIngredients, shouldRemove = true, currentDishIndex = -1) => {
            if (shouldRemove) {
                console.log('消耗食材:', usedIngredients)
            } else {
                console.log('恢复食材:', usedIngredients)
            }

            // 创建一个副本来跟踪还需要处理的食材
            const ingredientsToProcess = [...usedIngredients]

            if (shouldRemove) {
                // 移除食材逻辑
                foodInPot.forEach((foodState, index) => {
                    if (foodState.inPot) {
                        const ingredientName = foodPositions[index].name
                        const removeIndex = ingredientsToProcess.indexOf(ingredientName)

                        if (removeIndex !== -1) {
                            // 将食材标记为已消耗（完全移除，不再显示）
                            foodState.inPot = false
                            foodState.isConsumed = true // 新增：标记为已消耗
                            foodState.currentX = foodState.originalX
                            foodState.currentY = foodState.originalY
                            foodState.potOffsetX = 0
                            foodState.potOffsetY = 0
                            foodState.isAnimating = false
                            foodState.animationProgress = 0
                            foodState.scale = 1
                            foodState.opacity = 1

                            // 从待处理列表中删除这个食材（避免重复处理）
                            ingredientsToProcess.splice(removeIndex, 1)
                        }
                    }
                })
            } else {
                // 恢复食材逻辑 - 检查食材是否还被其他未完成的菜品需要
                foodInPot.forEach((foodState, index) => {
                    if (foodState.inPot) {
                        const ingredientName = foodPositions[index].name
                        const restoreIndex = ingredientsToProcess.indexOf(ingredientName)

                        if (restoreIndex !== -1) {
                            // 检查该食材是否还被其他未完成的菜品需要（排除当前正在制作的菜品）
                            const isNeededByOtherDishes = isIngredientNeededByOtherDishes(ingredientName, currentDishIndex)

                            if (isNeededByOtherDishes && !isGameEnded) {
                                // 如果食材还被其他未完成的菜品需要，就恢复到盘子里
                                foodState.inPot = false
                                foodState.isConsumed = false
                                foodState.currentX = foodState.originalX
                                foodState.currentY = foodState.originalY
                                foodState.potOffsetX = 0
                                foodState.potOffsetY = 0
                                foodState.isAnimating = false
                                foodState.animationProgress = 0
                                foodState.scale = 1
                                foodState.opacity = 1
                            } else {
                                // 如果食材不被其他未完成的菜品需要，就消耗掉
                                foodState.inPot = false
                                foodState.isConsumed = true
                                foodState.currentX = foodState.originalX
                                foodState.currentY = foodState.originalY
                                foodState.potOffsetX = 0
                                foodState.potOffsetY = 0
                                foodState.isAnimating = false
                                foodState.animationProgress = 0
                                foodState.scale = 1
                                foodState.opacity = 1
                            }

                            // 从待处理列表中删除这个食材（避免重复处理）
                            ingredientsToProcess.splice(restoreIndex, 1)
                        }
                    }
                })
            }
        }

        // 菜品掉落动画
        const animateDishToDrop = (dishAnimation) => {
            const currentTime = Date.now()
            const elapsed = currentTime - dishAnimation.animationStartTime
            const progress = Math.min(elapsed / dishAnimation.animationDuration, 1)

            // 使用缓动函数（easeInQuad - 重力效果）
            const easeProgress = progress * progress

            // 计算当前位置
            const startX = potCenter.x
            const startY = potCenter.y
            const endX = dishAnimation.targetX
            const endY = dishAnimation.targetY

            dishAnimation.currentX = startX + (endX - startX) * progress
            dishAnimation.currentY = startY + (endY - startY) * easeProgress

            // 绘制动画帧
            drawDishAnimation(dishAnimation)

            if (progress < 1) {
                // 继续动画
                requestAnimationFrame(() => animateDishToDrop(dishAnimation))
            } else {
                // 动画完成
                dishAnimation.isAnimating = false
                // 将对应盒子中的菜品设置为正常状态
                setDishNormal(dishAnimation.dishIndex)
                console.log(`${dishRecipes[dishAnimation.dishIndex].name} 制作完成！`)
                checkWinCondition()
            }
        }

        // 绘制菜品动画
        const drawDishAnimation = (dishAnimation) => {
            // 重新绘制整个场景
            if (!isVideoPlaying) {
                drawInitialFrame()
            }

            // 在食材canvas上绘制动画中的菜品
            const drawX = dishAnimation.currentX - dishAnimation.width / 2
            const drawY = dishAnimation.currentY - dishAnimation.height / 2

            foodCtx.drawImage(
                dishAnimation.image,
                drawX,
                drawY,
                dishAnimation.width,
                dishAnimation.height
            )
        }

        // 重置游戏状态（测试用）
        const resetGameState = () => {
            // 重置游戏结束状态
            isGameEnded = false

            // 重置所有食材状态
            initFoodStates()

            // 重置所有菜品状态为绿色
            dishStates.forEach(state => {
                state.isGreen = true
            })

            // 重新绘制
            if (!isVideoPlaying) {
                drawInitialFrame()
            }

            console.log('游戏状态已重置')
        }

        // 炒菜动画开始时间
        let cookingStartTime = null

        const prize = ref('')
        const link = ref('')
        if (startData.link) {
            console.log(aesDecrypt(startData.link));
            
            link.value = aesDecrypt(startData.link);
        }
        const scene = ref(0)
        var u = navigator.userAgent;
        if (u.indexOf("miniProgram") != -1) {
            //在微信小程序
            scene.value = 1
        } else {
            //在微信网页
            scene.value = 2
        }
        const goPrize = (link) => {
            wx.miniProgram.reLaunch({ url: `/${link}` })
        }
        const gameEnd = throttle(async (e) => {
            isGameEnded = true // 标记游戏已结束
            const res = await defaultHttp('gameEnd', {
                cg: e ? 1 : 0,
            }, { status: 1 })
            if (res.status === 1) {
                show.value = 8
                prize.value = res.data.prize
                link.value = aesDecrypt(res.data.link);
            } else if (res.status === 2) {
                // 第二关失败，其他地方弹框，这里不处理
            } else if (res.status === 4) {
                show.value = 10
            } else {
                vantAlert(res.msg, reload)
            }
        })
        const goLink = (p) => {
            location.replace(p)
        }
        return {
            on, bgClick,
            page, show, startData, start,
            gamestart, findingTimeFormat, foodList, findFood,
            tipMessages, removeTip, handleAreaClick, reload,
            fallingFoods, removeFallingFood,
            next,
            // 炒菜倒计时相关
            cookingTimeFormat,
            startCookingTimer,
            // 添加 ref 和炒菜游戏相关函数
            gameCanvas,
            foodCanvas,
            startButton,
            handleStartClick,
            prize,
            link,
            scene,
            goPrize,
            goLink,
            // 食材组合信息提示相关
            ingredientTooltips,
            foodPositions,
        }
    },
});
app.use(vant);
app.mount('#app');